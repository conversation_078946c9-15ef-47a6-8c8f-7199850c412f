Action Log - 2025-07-07 09:32:42
================================================================================

[[09:32:42]] [INFO] Generating execution report...
[[09:32:42]] [SUCCESS] All tests passed successfully!
[[09:32:42]] [SUCCESS] Screenshot refreshed
[[09:32:42]] [INFO] Refreshing screenshot...
[[09:32:42]] [INFO] xyHVihJMBi=pass
[[09:32:35]] [SUCCESS] Screenshot refreshed successfully
[[09:32:35]] [SUCCESS] Screenshot refreshed successfully
[[09:32:35]] [INFO] xyHVihJMBi=running
[[09:32:35]] [INFO] Executing action 51/51: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[09:32:35]] [SUCCESS] Screenshot refreshed
[[09:32:35]] [INFO] Refreshing screenshot...
[[09:32:35]] [INFO] mWeLQtXiL6=pass
[[09:32:30]] [SUCCESS] Screenshot refreshed successfully
[[09:32:30]] [SUCCESS] Screenshot refreshed successfully
[[09:32:30]] [INFO] mWeLQtXiL6=running
[[09:32:30]] [INFO] Executing action 50/51: Swipe from (50%, 70%) to (50%, 30%)
[[09:32:29]] [SUCCESS] Screenshot refreshed
[[09:32:29]] [INFO] Refreshing screenshot...
[[09:32:29]] [INFO] rkwVoJGZG4=pass
[[09:32:27]] [SUCCESS] Screenshot refreshed successfully
[[09:32:27]] [SUCCESS] Screenshot refreshed successfully
[[09:32:27]] [INFO] rkwVoJGZG4=running
[[09:32:27]] [INFO] Executing action 49/51: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[09:32:27]] [SUCCESS] Screenshot refreshed
[[09:32:27]] [INFO] Refreshing screenshot...
[[09:32:27]] [INFO] 0f2FSZYjWq=pass
[[09:32:20]] [SUCCESS] Screenshot refreshed successfully
[[09:32:20]] [SUCCESS] Screenshot refreshed successfully
[[09:32:20]] [INFO] 0f2FSZYjWq=running
[[09:32:20]] [INFO] Executing action 48/51: Check if element with text="3000" exists
[[09:32:20]] [SUCCESS] Screenshot refreshed
[[09:32:20]] [INFO] Refreshing screenshot...
[[09:32:20]] [INFO] Tebej51pT2=pass
[[09:32:18]] [SUCCESS] Screenshot refreshed successfully
[[09:32:18]] [SUCCESS] Screenshot refreshed successfully
[[09:32:18]] [INFO] Tebej51pT2=running
[[09:32:18]] [INFO] Executing action 47/51: Tap on element with xpath: //android.widget.TextView[@text="Continue shopping"]
[[09:32:17]] [SUCCESS] Screenshot refreshed
[[09:32:17]] [INFO] Refreshing screenshot...
[[09:32:17]] [INFO] JrPVGdts3J=pass
[[09:32:02]] [SUCCESS] Screenshot refreshed successfully
[[09:32:02]] [SUCCESS] Screenshot refreshed successfully
[[09:32:01]] [INFO] JrPVGdts3J=running
[[09:32:01]] [INFO] Executing action 46/51: Tap on image: bag-remove-btn-android.png
[[09:32:01]] [SUCCESS] Screenshot refreshed
[[09:32:01]] [INFO] Refreshing screenshot...
[[09:32:01]] [INFO] s8h8VDUIOC=pass
[[09:31:09]] [SUCCESS] Screenshot refreshed successfully
[[09:31:09]] [SUCCESS] Screenshot refreshed successfully
[[09:31:09]] [INFO] s8h8VDUIOC=running
[[09:31:09]] [INFO] Executing action 45/51: Swipe from (50%, 70%) to (50%, 30%)
[[09:31:08]] [SUCCESS] Screenshot refreshed
[[09:31:08]] [INFO] Refreshing screenshot...
[[09:31:08]] [INFO] GYK47u1y3A=pass
[[09:31:06]] [SUCCESS] Screenshot refreshed successfully
[[09:31:06]] [SUCCESS] Screenshot refreshed successfully
[[09:31:06]] [INFO] GYK47u1y3A=running
[[09:31:06]] [INFO] Executing action 44/51: Android Function: send_key_event - Key Event: TAB
[[09:31:06]] [SUCCESS] Screenshot refreshed
[[09:31:06]] [INFO] Refreshing screenshot...
[[09:31:06]] [INFO] ZWpYNcpbFA=pass
[[09:31:02]] [SUCCESS] Screenshot refreshed successfully
[[09:31:02]] [SUCCESS] Screenshot refreshed successfully
[[09:31:02]] [INFO] ZWpYNcpbFA=running
[[09:31:02]] [INFO] Executing action 43/51: Tap on Text: "VIC"
[[09:31:02]] [SUCCESS] Screenshot refreshed
[[09:31:02]] [INFO] Refreshing screenshot...
[[09:31:02]] [INFO] QpBLC6BStn=pass
[[09:30:52]] [INFO] QpBLC6BStn=running
[[09:30:52]] [INFO] Executing action 42/51: textClear action
[[09:30:52]] [INFO] G4A3KBlXHq=fail
[[09:30:52]] [ERROR] Action 41 failed: Error tapping on text: HTTPConnectionPool(host='127.0.0.1', port=4723): Read timed out. (read timeout=30.0)
[[09:30:16]] [SUCCESS] Screenshot refreshed successfully
[[09:30:16]] [SUCCESS] Screenshot refreshed successfully
[[09:30:16]] [INFO] G4A3KBlXHq=running
[[09:30:16]] [INFO] Executing action 41/51: Tap on Text: "Nearby"
[[09:30:16]] [SUCCESS] Screenshot refreshed
[[09:30:16]] [INFO] Refreshing screenshot...
[[09:30:16]] [INFO] 3gJsiap2Ds=pass
[[09:30:13]] [SUCCESS] Screenshot refreshed successfully
[[09:30:13]] [SUCCESS] Screenshot refreshed successfully
[[09:30:12]] [INFO] 3gJsiap2Ds=running
[[09:30:12]] [INFO] Executing action 40/51: Tap on Text: "Collect"
[[09:30:12]] [SUCCESS] Screenshot refreshed
[[09:30:12]] [INFO] Refreshing screenshot...
[[09:30:12]] [INFO] qofJDqXBME=pass
[[09:30:07]] [SUCCESS] Screenshot refreshed successfully
[[09:30:07]] [SUCCESS] Screenshot refreshed successfully
[[09:30:07]] [INFO] qofJDqXBME=running
[[09:30:07]] [INFO] Executing action 39/51: Wait till text appears: "Delivery"
[[09:30:06]] [SUCCESS] Screenshot refreshed
[[09:30:06]] [INFO] Refreshing screenshot...
[[09:30:06]] [INFO] rkwVoJGZG4=pass
[[09:30:05]] [SUCCESS] Screenshot refreshed successfully
[[09:30:05]] [SUCCESS] Screenshot refreshed successfully
[[09:30:05]] [INFO] rkwVoJGZG4=running
[[09:30:05]] [INFO] Executing action 38/51: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[09:30:04]] [SUCCESS] Screenshot refreshed
[[09:30:04]] [INFO] Refreshing screenshot...
[[09:30:04]] [INFO] 94ikwhIEE2=pass
[[09:30:00]] [SUCCESS] Screenshot refreshed successfully
[[09:30:00]] [SUCCESS] Screenshot refreshed successfully
[[09:29:18]] [INFO] 94ikwhIEE2=running
[[09:29:18]] [INFO] Executing action 37/51: Tap on Text: "bag"
[[09:29:17]] [SUCCESS] Screenshot refreshed
[[09:29:17]] [INFO] Refreshing screenshot...
[[09:29:17]] [INFO] DfwaiVZ8Z9=pass
[[09:29:14]] [SUCCESS] Screenshot refreshed successfully
[[09:29:14]] [SUCCESS] Screenshot refreshed successfully
[[09:29:14]] [INFO] DfwaiVZ8Z9=running
[[09:29:14]] [INFO] Executing action 36/51: Swipe from (50%, 70%) to (50%, 50%)
[[09:29:14]] [SUCCESS] Screenshot refreshed
[[09:29:14]] [INFO] Refreshing screenshot...
[[09:29:14]] [INFO] eRCmRhc3re=pass
[[09:29:11]] [SUCCESS] Screenshot refreshed successfully
[[09:29:11]] [SUCCESS] Screenshot refreshed successfully
[[09:29:11]] [INFO] eRCmRhc3re=running
[[09:29:11]] [INFO] Executing action 35/51: Check if element with text="Broadway" exists
[[09:29:10]] [SUCCESS] Screenshot refreshed
[[09:29:10]] [INFO] Refreshing screenshot...
[[09:29:10]] [INFO] E2jpN7BioW=pass
[[09:29:06]] [SUCCESS] Screenshot refreshed successfully
[[09:29:06]] [SUCCESS] Screenshot refreshed successfully
[[09:29:06]] [INFO] E2jpN7BioW=running
[[09:29:06]] [INFO] Executing action 34/51: Tap on element with accessibility_id: btnSaveOrContinue
[[09:29:05]] [SUCCESS] Screenshot refreshed
[[09:29:05]] [INFO] Refreshing screenshot...
[[09:29:05]] [INFO] kDnmoQJG4o=pass
[[09:29:03]] [SUCCESS] Screenshot refreshed successfully
[[09:29:03]] [SUCCESS] Screenshot refreshed successfully
[[09:29:03]] [INFO] kDnmoQJG4o=running
[[09:29:03]] [INFO] Executing action 33/51: Wait till accessibility_id=btnSaveOrContinue
[[09:29:03]] [SUCCESS] Screenshot refreshed
[[09:29:03]] [INFO] Refreshing screenshot...
[[09:29:03]] [INFO] H0ODFz7sWJ=pass
[[09:28:18]] [SUCCESS] Screenshot refreshed successfully
[[09:28:18]] [SUCCESS] Screenshot refreshed successfully
[[09:28:17]] [INFO] H0ODFz7sWJ=running
[[09:28:17]] [INFO] Executing action 32/51: Tap on Text: "2000"
[[09:28:17]] [SUCCESS] Screenshot refreshed
[[09:28:17]] [INFO] Refreshing screenshot...
[[09:28:17]] [INFO] pldheRUBVi=pass
[[09:28:14]] [SUCCESS] Screenshot refreshed successfully
[[09:28:14]] [SUCCESS] Screenshot refreshed successfully
[[09:28:14]] [INFO] pldheRUBVi=running
[[09:28:14]] [INFO] Executing action 31/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[09:28:14]] [SUCCESS] Screenshot refreshed
[[09:28:14]] [INFO] Refreshing screenshot...
[[09:28:14]] [INFO] uZHvvAzVfx=pass
[[09:28:11]] [SUCCESS] Screenshot refreshed successfully
[[09:28:11]] [SUCCESS] Screenshot refreshed successfully
[[09:28:11]] [INFO] uZHvvAzVfx=running
[[09:28:11]] [INFO] Executing action 30/51: textClear action
[[09:28:10]] [SUCCESS] Screenshot refreshed
[[09:28:10]] [INFO] Refreshing screenshot...
[[09:28:10]] [INFO] pldheRUBVi=pass
[[09:28:08]] [SUCCESS] Screenshot refreshed successfully
[[09:28:08]] [SUCCESS] Screenshot refreshed successfully
[[09:28:08]] [INFO] pldheRUBVi=running
[[09:28:08]] [INFO] Executing action 29/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[09:28:07]] [SUCCESS] Screenshot refreshed
[[09:28:07]] [INFO] Refreshing screenshot...
[[09:28:07]] [INFO] pldheRUBVi=pass
[[09:28:05]] [SUCCESS] Screenshot refreshed successfully
[[09:28:05]] [SUCCESS] Screenshot refreshed successfully
[[09:28:05]] [INFO] pldheRUBVi=running
[[09:28:05]] [INFO] Executing action 28/51: Wait till xpath=//android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[09:28:05]] [SUCCESS] Screenshot refreshed
[[09:28:05]] [INFO] Refreshing screenshot...
[[09:28:05]] [INFO] WmNWcsWVHv=pass
[[09:28:00]] [SUCCESS] Screenshot refreshed successfully
[[09:28:00]] [SUCCESS] Screenshot refreshed successfully
[[09:27:07]] [INFO] WmNWcsWVHv=running
[[09:27:07]] [INFO] Executing action 27/51: Tap on Text: "4000"
[[09:27:07]] [SUCCESS] Screenshot refreshed
[[09:27:07]] [INFO] Refreshing screenshot...
[[09:27:07]] [INFO] lnjoz8hHUU=pass
[[09:27:02]] [SUCCESS] Screenshot refreshed successfully
[[09:27:02]] [SUCCESS] Screenshot refreshed successfully
[[09:27:02]] [INFO] lnjoz8hHUU=running
[[09:27:02]] [INFO] Executing action 26/51: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[09:27:01]] [SUCCESS] Screenshot refreshed
[[09:27:01]] [INFO] Refreshing screenshot...
[[09:27:01]] [INFO] VkUKQbf1Qt=pass
[[09:26:16]] [SUCCESS] Screenshot refreshed successfully
[[09:26:16]] [SUCCESS] Screenshot refreshed successfully
[[09:26:16]] [INFO] VkUKQbf1Qt=running
[[09:26:16]] [INFO] Executing action 25/51: Tap on Text: "UNO"
[[09:26:15]] [SUCCESS] Screenshot refreshed
[[09:26:15]] [INFO] Refreshing screenshot...
[[09:26:15]] [INFO] 73NABkfWyY=pass
[[09:26:10]] [SUCCESS] Screenshot refreshed successfully
[[09:26:10]] [SUCCESS] Screenshot refreshed successfully
[[09:26:09]] [INFO] 73NABkfWyY=running
[[09:26:09]] [INFO] Executing action 24/51: Check if element with text="Toowong" exists
[[09:26:09]] [SUCCESS] Screenshot refreshed
[[09:26:09]] [INFO] Refreshing screenshot...
[[09:26:09]] [INFO] E2jpN7BioW=pass
[[09:26:06]] [SUCCESS] Screenshot refreshed successfully
[[09:26:06]] [SUCCESS] Screenshot refreshed successfully
[[09:26:05]] [INFO] E2jpN7BioW=running
[[09:26:05]] [INFO] Executing action 23/51: Tap on element with accessibility_id: btnSaveOrContinue
[[09:26:05]] [SUCCESS] Screenshot refreshed
[[09:26:05]] [INFO] Refreshing screenshot...
[[09:26:05]] [INFO] kDnmoQJG4o=pass
[[09:26:03]] [SUCCESS] Screenshot refreshed successfully
[[09:26:03]] [SUCCESS] Screenshot refreshed successfully
[[09:26:03]] [INFO] kDnmoQJG4o=running
[[09:26:03]] [INFO] Executing action 22/51: Wait till accessibility_id=btnSaveOrContinue
[[09:26:02]] [SUCCESS] Screenshot refreshed
[[09:26:02]] [INFO] Refreshing screenshot...
[[09:26:02]] [INFO] VkUKQbf1Qt=pass
[[09:25:58]] [SUCCESS] Screenshot refreshed successfully
[[09:25:58]] [SUCCESS] Screenshot refreshed successfully
[[09:25:56]] [INFO] VkUKQbf1Qt=running
[[09:25:56]] [INFO] Executing action 21/51: Tap on Text: "CITY"
[[09:25:56]] [SUCCESS] Screenshot refreshed
[[09:25:56]] [INFO] Refreshing screenshot...
[[09:25:56]] [INFO] pldheRUBVi=pass
[[09:25:53]] [SUCCESS] Screenshot refreshed successfully
[[09:25:53]] [SUCCESS] Screenshot refreshed successfully
[[09:25:53]] [INFO] pldheRUBVi=running
[[09:25:53]] [INFO] Executing action 20/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[09:25:52]] [SUCCESS] Screenshot refreshed
[[09:25:52]] [INFO] Refreshing screenshot...
[[09:25:52]] [INFO] kbdEPCPYod=pass
[[09:25:49]] [SUCCESS] Screenshot refreshed successfully
[[09:25:49]] [SUCCESS] Screenshot refreshed successfully
[[09:25:49]] [INFO] kbdEPCPYod=running
[[09:25:49]] [INFO] Executing action 19/51: textClear action
[[09:25:49]] [SUCCESS] Screenshot refreshed
[[09:25:49]] [INFO] Refreshing screenshot...
[[09:25:49]] [INFO] pldheRUBVi=pass
[[09:25:22]] [SUCCESS] Screenshot refreshed successfully
[[09:25:22]] [SUCCESS] Screenshot refreshed successfully
[[09:25:22]] [INFO] pldheRUBVi=running
[[09:25:22]] [INFO] Executing action 18/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[09:25:21]] [SUCCESS] Screenshot refreshed
[[09:25:21]] [INFO] Refreshing screenshot...
[[09:25:21]] [INFO] YhLhTn3Wtm=pass
[[09:25:15]] [SUCCESS] Screenshot refreshed successfully
[[09:25:15]] [SUCCESS] Screenshot refreshed successfully
[[09:25:14]] [INFO] YhLhTn3Wtm=running
[[09:25:14]] [INFO] Executing action 17/51: Wait for 5 ms
[[09:25:14]] [SUCCESS] Screenshot refreshed
[[09:25:14]] [INFO] Refreshing screenshot...
[[09:25:14]] [INFO] VkUKQbf1Qt=pass
[[09:25:09]] [SUCCESS] Screenshot refreshed successfully
[[09:25:09]] [SUCCESS] Screenshot refreshed successfully
[[09:25:08]] [INFO] VkUKQbf1Qt=running
[[09:25:08]] [INFO] Executing action 16/51: Tap on Text: "Edit"
[[09:25:08]] [SUCCESS] Screenshot refreshed
[[09:25:08]] [INFO] Refreshing screenshot...
[[09:25:08]] [INFO] MpdUKUazHa=pass
[[09:25:04]] [SUCCESS] Screenshot refreshed successfully
[[09:25:04]] [SUCCESS] Screenshot refreshed successfully
[[09:25:04]] [INFO] MpdUKUazHa=running
[[09:25:04]] [INFO] Executing action 15/51: Wait till image appears: sort-by-relevance-android.png
[[09:25:03]] [SUCCESS] Screenshot refreshed
[[09:25:03]] [INFO] Refreshing screenshot...
[[09:25:03]] [INFO] IupxLP2Jsr=pass
[[09:25:01]] [SUCCESS] Screenshot refreshed successfully
[[09:25:01]] [SUCCESS] Screenshot refreshed successfully
[[09:25:01]] [INFO] IupxLP2Jsr=running
[[09:25:01]] [INFO] Executing action 14/51: Input text: "P_6225544"
[[09:25:01]] [SUCCESS] Screenshot refreshed
[[09:25:01]] [INFO] Refreshing screenshot...
[[09:25:01]] [INFO] 70iOOakiG7=pass
[[09:24:57]] [SUCCESS] Screenshot refreshed successfully
[[09:24:57]] [SUCCESS] Screenshot refreshed successfully
[[09:24:53]] [INFO] 70iOOakiG7=running
[[09:24:53]] [INFO] Executing action 13/51: Tap on Text: "Find"
[[09:24:52]] [SUCCESS] Screenshot refreshed
[[09:24:52]] [INFO] Refreshing screenshot...
[[09:24:52]] [INFO] E2jpN7BioW=pass
[[09:24:48]] [SUCCESS] Screenshot refreshed successfully
[[09:24:48]] [SUCCESS] Screenshot refreshed successfully
[[09:24:48]] [INFO] E2jpN7BioW=running
[[09:24:48]] [INFO] Executing action 12/51: Tap on element with accessibility_id: btnSaveOrContinue
[[09:24:48]] [SUCCESS] Screenshot refreshed
[[09:24:48]] [INFO] Refreshing screenshot...
[[09:24:48]] [INFO] kDnmoQJG4o=pass
[[09:24:46]] [SUCCESS] Screenshot refreshed successfully
[[09:24:46]] [SUCCESS] Screenshot refreshed successfully
[[09:24:46]] [INFO] kDnmoQJG4o=running
[[09:24:46]] [INFO] Executing action 11/51: Wait till accessibility_id=btnSaveOrContinue
[[09:24:45]] [SUCCESS] Screenshot refreshed
[[09:24:45]] [INFO] Refreshing screenshot...
[[09:24:45]] [INFO] mw9GQ4mzRE=pass
[[09:24:41]] [SUCCESS] Screenshot refreshed successfully
[[09:24:41]] [SUCCESS] Screenshot refreshed successfully
[[09:24:39]] [INFO] mw9GQ4mzRE=running
[[09:24:39]] [INFO] Executing action 10/51: Tap on Text: "BC"
[[09:24:38]] [SUCCESS] Screenshot refreshed
[[09:24:38]] [INFO] Refreshing screenshot...
[[09:24:38]] [INFO] pldheRUBVi=pass
[[09:24:35]] [SUCCESS] Screenshot refreshed successfully
[[09:24:35]] [SUCCESS] Screenshot refreshed successfully
[[09:24:35]] [INFO] pldheRUBVi=running
[[09:24:35]] [INFO] Executing action 9/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[09:24:34]] [SUCCESS] Screenshot refreshed
[[09:24:34]] [INFO] Refreshing screenshot...
[[09:24:34]] [INFO] kbdEPCPYod=pass
[[09:24:31]] [SUCCESS] Screenshot refreshed successfully
[[09:24:31]] [SUCCESS] Screenshot refreshed successfully
[[09:24:31]] [INFO] kbdEPCPYod=running
[[09:24:31]] [INFO] Executing action 8/51: textClear action
[[09:24:31]] [SUCCESS] Screenshot refreshed
[[09:24:31]] [INFO] Refreshing screenshot...
[[09:24:31]] [INFO] pldheRUBVi=pass
[[09:24:29]] [SUCCESS] Screenshot refreshed successfully
[[09:24:29]] [SUCCESS] Screenshot refreshed successfully
[[09:24:28]] [INFO] pldheRUBVi=running
[[09:24:28]] [INFO] Executing action 7/51: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[09:24:28]] [SUCCESS] Screenshot refreshed
[[09:24:28]] [INFO] Refreshing screenshot...
[[09:24:28]] [INFO] QMXBlswP6H=pass
[[09:24:13]] [SUCCESS] Screenshot refreshed successfully
[[09:24:13]] [SUCCESS] Screenshot refreshed successfully
[[09:24:13]] [INFO] QMXBlswP6H=running
[[09:24:13]] [INFO] Executing action 6/51: Tap on Text: "Edit"
[[09:24:12]] [SUCCESS] Screenshot refreshed
[[09:24:12]] [INFO] Refreshing screenshot...
[[09:24:12]] [INFO] RLz6vQo3ag=pass
[[09:24:07]] [INFO] RLz6vQo3ag=running
[[09:24:07]] [INFO] Executing action 5/51: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[09:24:07]] [SUCCESS] Screenshot refreshed
[[09:24:07]] [INFO] Refreshing screenshot...
[[09:24:07]] [SUCCESS] Screenshot refreshed
[[09:24:07]] [INFO] Refreshing screenshot...
[[09:24:05]] [SUCCESS] Screenshot refreshed successfully
[[09:24:05]] [SUCCESS] Screenshot refreshed successfully
[[09:24:05]] [INFO] Executing Multi Step action step 5/5: Input text: "Wonderbaby@5"
[[09:24:04]] [SUCCESS] Screenshot refreshed
[[09:24:04]] [INFO] Refreshing screenshot...
[[09:24:02]] [SUCCESS] Screenshot refreshed successfully
[[09:24:02]] [SUCCESS] Screenshot refreshed successfully
[[09:24:02]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[09:24:02]] [SUCCESS] Screenshot refreshed
[[09:24:02]] [INFO] Refreshing screenshot...
[[09:23:58]] [SUCCESS] Screenshot refreshed successfully
[[09:23:58]] [SUCCESS] Screenshot refreshed successfully
[[09:23:58]] [INFO] Executing Multi Step action step 3/5: Input text: "<EMAIL>"
[[09:23:57]] [SUCCESS] Screenshot refreshed
[[09:23:57]] [INFO] Refreshing screenshot...
[[09:23:56]] [SUCCESS] Screenshot refreshed successfully
[[09:23:56]] [SUCCESS] Screenshot refreshed successfully
[[09:23:55]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[09:23:55]] [SUCCESS] Screenshot refreshed
[[09:23:55]] [INFO] Refreshing screenshot...
[[09:23:44]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[09:23:44]] [INFO] Loaded 5 steps from test case: Kmart-Signin-AU-ANDROID
[[09:23:44]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[09:23:44]] [INFO] xz8njynjpZ=running
[[09:23:44]] [INFO] Executing action 4/51: Execute Test Case: Kmart-Signin-AU-ANDROID (5 steps)
[[09:23:44]] [INFO] J9loj6Zl5K=fail
[[09:23:44]] [ERROR] Action 3 failed: Element with xpath '//android.widget.EditText[@resource-id="username"]' not found within timeout of 10.0 seconds
[[09:23:24]] [SUCCESS] Screenshot refreshed successfully
[[09:23:24]] [SUCCESS] Screenshot refreshed successfully
[[09:23:23]] [INFO] J9loj6Zl5K=running
[[09:23:23]] [INFO] Executing action 3/51: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[09:23:23]] [SUCCESS] Screenshot refreshed
[[09:23:23]] [INFO] Refreshing screenshot...
[[09:23:23]] [INFO] Y8vz7AJD1i=pass
[[09:23:19]] [SUCCESS] Screenshot refreshed successfully
[[09:23:19]] [SUCCESS] Screenshot refreshed successfully
[[09:23:19]] [INFO] Y8vz7AJD1i=running
[[09:23:19]] [INFO] Executing action 2/51: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[09:23:18]] [SUCCESS] Screenshot refreshed
[[09:23:18]] [INFO] Refreshing screenshot...
[[09:23:18]] [INFO] H9fy9qcFbZ=pass
[[09:23:15]] [INFO] H9fy9qcFbZ=running
[[09:23:15]] [INFO] Executing action 1/51: Launch app: au.com.kmart
[[09:23:15]] [INFO] ExecutionManager: Starting execution of 51 actions...
[[09:23:15]] [SUCCESS] Cleared 1 screenshots from database
[[09:23:15]] [INFO] Clearing screenshots from database before execution...
[[09:23:15]] [SUCCESS] All screenshots deleted successfully
[[09:23:15]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[09:23:15]] [INFO] Skipping report initialization - single test case execution
[[09:23:12]] [SUCCESS] All screenshots deleted successfully
[[09:23:12]] [SUCCESS] Loaded test case "Postcode Flow_AU_ANDROID" with 51 actions
[[09:23:12]] [SUCCESS] Added action: tap
[[09:23:12]] [SUCCESS] Added action: swipe
[[09:23:12]] [SUCCESS] Added action: tap
[[09:23:12]] [SUCCESS] Added action: exists
[[09:23:12]] [SUCCESS] Added action: tap
[[09:23:12]] [SUCCESS] Added action: tap
[[09:23:12]] [SUCCESS] Added action: swipe
[[09:23:12]] [SUCCESS] Added action: androidFunctions
[[09:23:12]] [SUCCESS] Added action: tapOnText
[[09:23:12]] [SUCCESS] Added action: textClear
[[09:23:12]] [SUCCESS] Added action: tapOnText
[[09:23:12]] [SUCCESS] Added action: tapOnText
[[09:23:12]] [SUCCESS] Added action: waitTill
[[09:23:12]] [SUCCESS] Added action: tap
[[09:23:12]] [SUCCESS] Added action: tapOnText
[[09:23:12]] [SUCCESS] Added action: swipe
[[09:23:12]] [SUCCESS] Added action: exists
[[09:23:12]] [SUCCESS] Added action: tap
[[09:23:12]] [SUCCESS] Added action: waitTill
[[09:23:12]] [SUCCESS] Added action: tapOnText
[[09:23:12]] [SUCCESS] Added action: tap
[[09:23:12]] [SUCCESS] Added action: textClear
[[09:23:12]] [SUCCESS] Added action: tap
[[09:23:12]] [SUCCESS] Added action: waitTill
[[09:23:12]] [SUCCESS] Added action: tapOnText
[[09:23:12]] [SUCCESS] Added action: waitTill
[[09:23:12]] [SUCCESS] Added action: tapOnText
[[09:23:12]] [SUCCESS] Added action: exists
[[09:23:12]] [SUCCESS] Added action: tap
[[09:23:12]] [SUCCESS] Added action: waitTill
[[09:23:12]] [SUCCESS] Added action: tapOnText
[[09:23:12]] [SUCCESS] Added action: tap
[[09:23:12]] [SUCCESS] Added action: textClear
[[09:23:12]] [SUCCESS] Added action: tap
[[09:23:12]] [SUCCESS] Added action: wait
[[09:23:12]] [SUCCESS] Added action: tapOnText
[[09:23:12]] [SUCCESS] Added action: waitTill
[[09:23:12]] [SUCCESS] Added action: text
[[09:23:12]] [SUCCESS] Added action: tapOnText
[[09:23:12]] [SUCCESS] Added action: tap
[[09:23:12]] [SUCCESS] Added action: waitTill
[[09:23:12]] [SUCCESS] Added action: tapOnText
[[09:23:12]] [SUCCESS] Added action: tap
[[09:23:12]] [SUCCESS] Added action: textClear
[[09:23:12]] [SUCCESS] Added action: tap
[[09:23:12]] [SUCCESS] Added action: tapOnText
[[09:23:12]] [SUCCESS] Added action: waitTill
[[09:23:12]] [SUCCESS] Added action: multiStep
[[09:23:12]] [SUCCESS] Added action: waitTill
[[09:23:12]] [SUCCESS] Added action: tap
[[09:23:12]] [SUCCESS] Added action: launchApp
[[09:23:12]] [INFO] All actions cleared
[[09:23:12]] [INFO] Cleaning up screenshots...
[[09:23:05]] [SUCCESS] Screenshot refreshed successfully
[[09:23:05]] [SUCCESS] Screenshot refreshed
[[09:23:05]] [INFO] Refreshing screenshot...
[[09:23:04]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8 with AirTest support
[[09:23:04]] [INFO] Device info updated: RMX2151
[[09:22:58]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[09:22:57]] [SUCCESS] Found 1 device(s)
[[09:22:57]] [INFO] Refreshing device list...
