Action Log - 2025-07-06 18:45:52
================================================================================

[[18:45:52]] [INFO] Generating execution report...
[[18:45:52]] [SUCCESS] All tests passed successfully!
[[18:45:52]] [SUCCESS] Screenshot refreshed
[[18:45:52]] [INFO] Refreshing screenshot...
[[18:45:52]] [SUCCESS] Screenshot refreshed
[[18:45:52]] [INFO] Refreshing screenshot...
[[18:45:49]] [SUCCESS] Screenshot refreshed successfully
[[18:45:49]] [SUCCESS] Screenshot refreshed successfully
[[18:45:49]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[18:45:49]] [SUCCESS] Screenshot refreshed
[[18:45:49]] [INFO] Refreshing screenshot...
[[18:45:36]] [SUCCESS] Screenshot refreshed successfully
[[18:45:36]] [SUCCESS] Screenshot refreshed successfully
[[18:45:36]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[18:45:36]] [SUCCESS] Screenshot refreshed
[[18:45:36]] [INFO] Refreshing screenshot...
[[18:45:32]] [SUCCESS] Screenshot refreshed successfully
[[18:45:32]] [SUCCESS] Screenshot refreshed successfully
[[18:45:32]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[18:45:31]] [SUCCESS] Screenshot refreshed
[[18:45:31]] [INFO] Refreshing screenshot...
[[18:45:28]] [SUCCESS] Screenshot refreshed successfully
[[18:45:28]] [SUCCESS] Screenshot refreshed successfully
[[18:45:28]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:45:27]] [SUCCESS] Screenshot refreshed
[[18:45:27]] [INFO] Refreshing screenshot...
[[18:45:21]] [SUCCESS] Screenshot refreshed successfully
[[18:45:21]] [SUCCESS] Screenshot refreshed successfully
[[18:45:20]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[18:45:20]] [SUCCESS] Screenshot refreshed
[[18:45:20]] [INFO] Refreshing screenshot...
[[18:45:15]] [SUCCESS] Screenshot refreshed successfully
[[18:45:15]] [SUCCESS] Screenshot refreshed successfully
[[18:45:14]] [INFO] Executing Multi Step action step 1/6: Restart app: nz.com.kmart
[[18:45:14]] [INFO] Loaded 6 steps from test case: Kmart_NZ_Cleanup
[[18:45:14]] [INFO] Loading steps for cleanupSteps action: Kmart_NZ_Cleanup
[[18:45:14]] [INFO] 2L5CHzd7qs=running
[[18:45:14]] [INFO] Executing action 50/50: cleanupSteps action
[[18:45:14]] [SUCCESS] Screenshot refreshed
[[18:45:14]] [INFO] Refreshing screenshot...
[[18:45:14]] [INFO] DGzGrOfbSq=pass
[[18:45:09]] [SUCCESS] Screenshot refreshed successfully
[[18:45:09]] [SUCCESS] Screenshot refreshed successfully
[[18:45:09]] [INFO] DGzGrOfbSq=running
[[18:45:09]] [INFO] Executing action 49/50: Swipe from (50%, 70%) to (50%, 10%)
[[18:45:08]] [SUCCESS] Screenshot refreshed
[[18:45:08]] [INFO] Refreshing screenshot...
[[18:45:08]] [INFO] OyUowAaBzD=pass
[[18:45:04]] [SUCCESS] Screenshot refreshed successfully
[[18:45:04]] [SUCCESS] Screenshot refreshed successfully
[[18:45:04]] [INFO] OyUowAaBzD=running
[[18:45:04]] [INFO] Executing action 48/50: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:45:03]] [SUCCESS] Screenshot refreshed
[[18:45:03]] [INFO] Refreshing screenshot...
[[18:45:03]] [INFO] jIeR7BPEPu=pass
[[18:44:59]] [SUCCESS] Screenshot refreshed successfully
[[18:44:59]] [SUCCESS] Screenshot refreshed successfully
[[18:44:59]] [INFO] jIeR7BPEPu=running
[[18:44:59]] [INFO] Executing action 47/50: Swipe from (50%, 70%) to (50%, 30%)
[[18:44:59]] [SUCCESS] Screenshot refreshed
[[18:44:59]] [INFO] Refreshing screenshot...
[[18:44:59]] [INFO] k3mu9Mt7Ec=pass
[[18:44:55]] [SUCCESS] Screenshot refreshed successfully
[[18:44:55]] [SUCCESS] Screenshot refreshed successfully
[[18:44:55]] [INFO] k3mu9Mt7Ec=running
[[18:44:55]] [INFO] Executing action 46/50: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:44:54]] [SUCCESS] Screenshot refreshed
[[18:44:54]] [INFO] Refreshing screenshot...
[[18:44:54]] [INFO] yhmzeynQyu=pass
[[18:44:50]] [SUCCESS] Screenshot refreshed successfully
[[18:44:50]] [SUCCESS] Screenshot refreshed successfully
[[18:44:50]] [INFO] yhmzeynQyu=running
[[18:44:50]] [INFO] Executing action 45/50: Tap on Text: "Remove"
[[18:44:49]] [SUCCESS] Screenshot refreshed
[[18:44:49]] [INFO] Refreshing screenshot...
[[18:44:49]] [INFO] Q0fomJIDoQ=pass
[[18:44:44]] [SUCCESS] Screenshot refreshed successfully
[[18:44:44]] [SUCCESS] Screenshot refreshed successfully
[[18:44:44]] [INFO] Q0fomJIDoQ=running
[[18:44:44]] [INFO] Executing action 44/50: If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[18:44:43]] [SUCCESS] Screenshot refreshed
[[18:44:43]] [INFO] Refreshing screenshot...
[[18:44:43]] [INFO] yhmzeynQyu=pass
[[18:44:39]] [SUCCESS] Screenshot refreshed successfully
[[18:44:39]] [SUCCESS] Screenshot refreshed successfully
[[18:44:38]] [INFO] yhmzeynQyu=running
[[18:44:38]] [INFO] Executing action 43/50: Tap on Text: "Remove"
[[18:44:38]] [SUCCESS] Screenshot refreshed
[[18:44:38]] [INFO] Refreshing screenshot...
[[18:44:38]] [INFO] Q0fomJIDoQ=pass
[[18:44:33]] [SUCCESS] Screenshot refreshed successfully
[[18:44:33]] [SUCCESS] Screenshot refreshed successfully
[[18:44:32]] [INFO] Q0fomJIDoQ=running
[[18:44:32]] [INFO] Executing action 42/50: If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[18:44:32]] [SUCCESS] Screenshot refreshed
[[18:44:32]] [INFO] Refreshing screenshot...
[[18:44:32]] [INFO] F1olhgKhUt=pass
[[18:44:28]] [SUCCESS] Screenshot refreshed successfully
[[18:44:28]] [SUCCESS] Screenshot refreshed successfully
[[18:44:27]] [INFO] F1olhgKhUt=running
[[18:44:27]] [INFO] Executing action 41/50: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[18:44:27]] [SUCCESS] Screenshot refreshed
[[18:44:27]] [INFO] Refreshing screenshot...
[[18:44:27]] [INFO] 8umPSX0vrr=pass
[[18:44:23]] [SUCCESS] Screenshot refreshed successfully
[[18:44:23]] [SUCCESS] Screenshot refreshed successfully
[[18:44:23]] [INFO] 8umPSX0vrr=running
[[18:44:23]] [INFO] Executing action 40/50: Tap on image: banner-close-updated.png
[[18:44:22]] [SUCCESS] Screenshot refreshed
[[18:44:22]] [INFO] Refreshing screenshot...
[[18:44:22]] [INFO] pr9o8Zsm5p=pass
[[18:44:20]] [SUCCESS] Screenshot refreshed successfully
[[18:44:20]] [SUCCESS] Screenshot refreshed successfully
[[18:44:18]] [INFO] pr9o8Zsm5p=running
[[18:44:18]] [INFO] Executing action 39/50: Tap on element with xpath: //XCUIElementTypeButton[@name="Move to wishlist"]
[[18:44:17]] [SUCCESS] Screenshot refreshed
[[18:44:17]] [INFO] Refreshing screenshot...
[[18:44:17]] [INFO] Qbg9bipTGs=pass
[[18:44:14]] [SUCCESS] Screenshot refreshed successfully
[[18:44:14]] [SUCCESS] Screenshot refreshed successfully
[[18:44:14]] [INFO] Qbg9bipTGs=running
[[18:44:14]] [INFO] Executing action 38/50: Wait till xpath=//XCUIElementTypeButton[@name="Move to wishlist"]
[[18:44:13]] [SUCCESS] Screenshot refreshed
[[18:44:13]] [INFO] Refreshing screenshot...
[[18:44:13]] [INFO] jIeR7BPEPu=pass
[[18:44:07]] [SUCCESS] Screenshot refreshed successfully
[[18:44:07]] [SUCCESS] Screenshot refreshed successfully
[[18:44:07]] [INFO] jIeR7BPEPu=running
[[18:44:07]] [INFO] Executing action 37/50: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Move to wishlist"]" is visible
[[18:44:06]] [SUCCESS] Screenshot refreshed
[[18:44:06]] [INFO] Refreshing screenshot...
[[18:44:06]] [INFO] Tch2A5RFzv=pass
[[18:43:55]] [INFO] Tch2A5RFzv=running
[[18:43:55]] [INFO] Executing action 36/50: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[18:43:55]] [SUCCESS] Screenshot refreshed successfully
[[18:43:55]] [SUCCESS] Screenshot refreshed successfully
[[18:43:54]] [SUCCESS] Screenshot refreshed
[[18:43:54]] [INFO] Refreshing screenshot...
[[18:43:54]] [INFO] lWIRxRm6HE=pass
[[18:43:50]] [SUCCESS] Screenshot refreshed successfully
[[18:43:50]] [SUCCESS] Screenshot refreshed successfully
[[18:43:50]] [INFO] lWIRxRm6HE=running
[[18:43:50]] [INFO] Executing action 35/50: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:43:49]] [SUCCESS] Screenshot refreshed
[[18:43:49]] [INFO] Refreshing screenshot...
[[18:43:49]] [INFO] uOt2cFGhGr=pass
[[18:43:45]] [SUCCESS] Screenshot refreshed successfully
[[18:43:45]] [SUCCESS] Screenshot refreshed successfully
[[18:43:44]] [INFO] uOt2cFGhGr=running
[[18:43:44]] [INFO] Executing action 34/50: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:43:44]] [SUCCESS] Screenshot refreshed
[[18:43:44]] [INFO] Refreshing screenshot...
[[18:43:44]] [INFO] Q0fomJIDoQ=pass
[[18:43:40]] [SUCCESS] Screenshot refreshed successfully
[[18:43:40]] [SUCCESS] Screenshot refreshed successfully
[[18:43:40]] [INFO] Q0fomJIDoQ=running
[[18:43:40]] [INFO] Executing action 33/50: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[1]
[[18:43:39]] [SUCCESS] Screenshot refreshed
[[18:43:39]] [INFO] Refreshing screenshot...
[[18:43:39]] [INFO] yhmzeynQyu=pass
[[18:43:35]] [SUCCESS] Screenshot refreshed successfully
[[18:43:35]] [SUCCESS] Screenshot refreshed successfully
[[18:43:35]] [INFO] yhmzeynQyu=running
[[18:43:35]] [INFO] Executing action 32/50: Tap on Text: "Remove"
[[18:43:34]] [SUCCESS] Screenshot refreshed
[[18:43:34]] [INFO] Refreshing screenshot...
[[18:43:34]] [INFO] YNMjmPGi1h=pass
[[18:43:28]] [SUCCESS] Screenshot refreshed successfully
[[18:43:28]] [SUCCESS] Screenshot refreshed successfully
[[18:43:27]] [INFO] YNMjmPGi1h=running
[[18:43:27]] [INFO] Executing action 31/50: Wait for 5 ms
[[18:43:27]] [SUCCESS] Screenshot refreshed
[[18:43:27]] [INFO] Refreshing screenshot...
[[18:43:27]] [INFO] Q0fomJIDoQ=pass
[[18:43:24]] [SUCCESS] Screenshot refreshed successfully
[[18:43:24]] [SUCCESS] Screenshot refreshed successfully
[[18:43:23]] [INFO] Q0fomJIDoQ=running
[[18:43:23]] [INFO] Executing action 30/50: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[2]/following-sibling::XCUIElementTypeImage[2]
[[18:43:22]] [SUCCESS] Screenshot refreshed
[[18:43:22]] [INFO] Refreshing screenshot...
[[18:43:22]] [INFO] y4i304JeJj=pass
[[18:43:17]] [SUCCESS] Screenshot refreshed successfully
[[18:43:17]] [SUCCESS] Screenshot refreshed successfully
[[18:43:17]] [INFO] y4i304JeJj=running
[[18:43:17]] [INFO] Executing action 29/50: Tap on Text: "Move"
[[18:43:17]] [SUCCESS] Screenshot refreshed
[[18:43:17]] [INFO] Refreshing screenshot...
[[18:43:17]] [INFO] 6pAgesZD43=pass
[[18:43:10]] [SUCCESS] Screenshot refreshed successfully
[[18:43:10]] [SUCCESS] Screenshot refreshed successfully
[[18:43:10]] [INFO] 6pAgesZD43=running
[[18:43:10]] [INFO] Executing action 28/50: Wait for 5 ms
[[18:43:09]] [SUCCESS] Screenshot refreshed
[[18:43:09]] [INFO] Refreshing screenshot...
[[18:43:09]] [INFO] Q0fomJIDoQ=pass
[[18:43:05]] [SUCCESS] Screenshot refreshed successfully
[[18:43:05]] [SUCCESS] Screenshot refreshed successfully
[[18:43:05]] [INFO] Q0fomJIDoQ=running
[[18:43:05]] [INFO] Executing action 27/50: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[18:43:05]] [SUCCESS] Screenshot refreshed
[[18:43:05]] [INFO] Refreshing screenshot...
[[18:43:05]] [INFO] Q0fomJIDoQ=pass
[[18:43:02]] [SUCCESS] Screenshot refreshed successfully
[[18:43:02]] [SUCCESS] Screenshot refreshed successfully
[[18:43:01]] [INFO] Q0fomJIDoQ=running
[[18:43:01]] [INFO] Executing action 26/50: Wait till xpath=(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[18:43:01]] [SUCCESS] Screenshot refreshed
[[18:43:01]] [INFO] Refreshing screenshot...
[[18:43:01]] [INFO] F1olhgKhUt=pass
[[18:42:57]] [SUCCESS] Screenshot refreshed successfully
[[18:42:57]] [SUCCESS] Screenshot refreshed successfully
[[18:42:57]] [INFO] F1olhgKhUt=running
[[18:42:57]] [INFO] Executing action 25/50: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[18:42:56]] [SUCCESS] Screenshot refreshed
[[18:42:56]] [INFO] Refreshing screenshot...
[[18:42:56]] [INFO] alaozIePOy=pass
[[18:42:51]] [SUCCESS] Screenshot refreshed successfully
[[18:42:51]] [SUCCESS] Screenshot refreshed successfully
[[18:42:51]] [INFO] alaozIePOy=running
[[18:42:51]] [INFO] Executing action 24/50: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[18:42:51]] [SUCCESS] Screenshot refreshed
[[18:42:51]] [INFO] Refreshing screenshot...
[[18:42:51]] [INFO] wj9NNcZwO8=pass
[[18:42:43]] [SUCCESS] Screenshot refreshed successfully
[[18:42:43]] [SUCCESS] Screenshot refreshed successfully
[[18:42:43]] [INFO] wj9NNcZwO8=running
[[18:42:43]] [INFO] Executing action 23/50: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[18:42:42]] [SUCCESS] Screenshot refreshed
[[18:42:42]] [INFO] Refreshing screenshot...
[[18:42:42]] [INFO] BzTvnSrykE=pass
[[18:42:39]] [SUCCESS] Screenshot refreshed successfully
[[18:42:39]] [SUCCESS] Screenshot refreshed successfully
[[18:42:38]] [INFO] BzTvnSrykE=running
[[18:42:38]] [INFO] Executing action 22/50: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:42:38]] [SUCCESS] Screenshot refreshed
[[18:42:38]] [INFO] Refreshing screenshot...
[[18:42:38]] [INFO] PH8FFnzohm=pass
[[18:42:34]] [SUCCESS] Screenshot refreshed successfully
[[18:42:34]] [SUCCESS] Screenshot refreshed successfully
[[18:42:34]] [INFO] PH8FFnzohm=running
[[18:42:34]] [INFO] Executing action 21/50: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1]
[[18:42:33]] [SUCCESS] Screenshot refreshed
[[18:42:33]] [INFO] Refreshing screenshot...
[[18:42:33]] [INFO] k404YWYgSk=pass
[[18:42:30]] [SUCCESS] Screenshot refreshed successfully
[[18:42:30]] [SUCCESS] Screenshot refreshed successfully
[[18:42:29]] [INFO] k404YWYgSk=running
[[18:42:29]] [INFO] Executing action 20/50: Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1]
[[18:42:29]] [SUCCESS] Screenshot refreshed
[[18:42:29]] [INFO] Refreshing screenshot...
[[18:42:29]] [INFO] Z86YU6Mq0g=pass
[[18:42:25]] [SUCCESS] Screenshot refreshed successfully
[[18:42:25]] [SUCCESS] Screenshot refreshed successfully
[[18:42:24]] [INFO] Z86YU6Mq0g=running
[[18:42:24]] [INFO] Executing action 19/50: Tap on image: env[device-back-img]
[[18:42:24]] [SUCCESS] Screenshot refreshed
[[18:42:24]] [INFO] Refreshing screenshot...
[[18:42:24]] [INFO] alaozIePOy=pass
[[18:42:18]] [SUCCESS] Screenshot refreshed successfully
[[18:42:18]] [SUCCESS] Screenshot refreshed successfully
[[18:42:17]] [INFO] alaozIePOy=running
[[18:42:17]] [INFO] Executing action 18/50: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[18:42:17]] [SUCCESS] Screenshot refreshed
[[18:42:17]] [INFO] Refreshing screenshot...
[[18:42:17]] [INFO] W9exkA2PNL=pass
[[18:42:10]] [SUCCESS] Screenshot refreshed successfully
[[18:42:10]] [SUCCESS] Screenshot refreshed successfully
[[18:42:09]] [INFO] W9exkA2PNL=running
[[18:42:09]] [INFO] Executing action 17/50: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[18:42:08]] [SUCCESS] Screenshot refreshed
[[18:42:08]] [INFO] Refreshing screenshot...
[[18:42:08]] [INFO] BzTvnSrykE=pass
[[18:42:05]] [SUCCESS] Screenshot refreshed successfully
[[18:42:05]] [SUCCESS] Screenshot refreshed successfully
[[18:42:04]] [INFO] BzTvnSrykE=running
[[18:42:04]] [INFO] Executing action 16/50: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:42:04]] [SUCCESS] Screenshot refreshed
[[18:42:04]] [INFO] Refreshing screenshot...
[[18:42:04]] [INFO] oWLIFhrzr1=pass
[[18:41:59]] [SUCCESS] Screenshot refreshed successfully
[[18:41:59]] [SUCCESS] Screenshot refreshed successfully
[[18:41:59]] [INFO] oWLIFhrzr1=running
[[18:41:59]] [INFO] Executing action 15/50: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[2]//XCUIElementTypeLink)[1]
[[18:41:58]] [SUCCESS] Screenshot refreshed
[[18:41:58]] [INFO] Refreshing screenshot...
[[18:41:58]] [INFO] zzd5ufNDih=pass
[[18:41:54]] [SUCCESS] Screenshot refreshed successfully
[[18:41:54]] [SUCCESS] Screenshot refreshed successfully
[[18:41:54]] [INFO] zzd5ufNDih=running
[[18:41:54]] [INFO] Executing action 14/50: Tap on image: env[device-back-img]
[[18:41:54]] [SUCCESS] Screenshot refreshed
[[18:41:54]] [INFO] Refreshing screenshot...
[[18:41:54]] [INFO] WbxRVpWtjw=pass
[[18:41:47]] [SUCCESS] Screenshot refreshed successfully
[[18:41:47]] [SUCCESS] Screenshot refreshed successfully
[[18:41:47]] [INFO] WbxRVpWtjw=running
[[18:41:47]] [INFO] Executing action 13/50: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[18:41:46]] [SUCCESS] Screenshot refreshed
[[18:41:46]] [INFO] Refreshing screenshot...
[[18:41:46]] [INFO] Pfw7jw4XEw=pass
[[18:41:39]] [SUCCESS] Screenshot refreshed successfully
[[18:41:39]] [SUCCESS] Screenshot refreshed successfully
[[18:41:38]] [INFO] Pfw7jw4XEw=running
[[18:41:38]] [INFO] Executing action 12/50: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[18:41:38]] [SUCCESS] Screenshot refreshed
[[18:41:38]] [INFO] Refreshing screenshot...
[[18:41:38]] [INFO] ITHvSyXXmu=pass
[[18:41:34]] [SUCCESS] Screenshot refreshed successfully
[[18:41:34]] [SUCCESS] Screenshot refreshed successfully
[[18:41:34]] [INFO] ITHvSyXXmu=running
[[18:41:34]] [INFO] Executing action 11/50: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:41:33]] [SUCCESS] Screenshot refreshed
[[18:41:33]] [INFO] Refreshing screenshot...
[[18:41:33]] [INFO] eLxHVWKeDQ=pass
[[18:41:29]] [SUCCESS] Screenshot refreshed successfully
[[18:41:29]] [SUCCESS] Screenshot refreshed successfully
[[18:41:29]] [INFO] eLxHVWKeDQ=running
[[18:41:29]] [INFO] Executing action 10/50: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:41:28]] [SUCCESS] Screenshot refreshed
[[18:41:28]] [INFO] Refreshing screenshot...
[[18:41:28]] [INFO] eLxHVWKeDQ=pass
[[18:41:25]] [SUCCESS] Screenshot refreshed successfully
[[18:41:25]] [SUCCESS] Screenshot refreshed successfully
[[18:41:24]] [INFO] eLxHVWKeDQ=running
[[18:41:24]] [INFO] Executing action 9/50: Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:41:24]] [SUCCESS] Screenshot refreshed
[[18:41:24]] [INFO] Refreshing screenshot...
[[18:41:24]] [INFO] nAB6Q8LAdv=pass
[[18:41:20]] [SUCCESS] Screenshot refreshed successfully
[[18:41:20]] [SUCCESS] Screenshot refreshed successfully
[[18:41:20]] [INFO] nAB6Q8LAdv=running
[[18:41:20]] [INFO] Executing action 8/50: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:41:19]] [SUCCESS] Screenshot refreshed
[[18:41:19]] [INFO] Refreshing screenshot...
[[18:41:19]] [INFO] sc2KH9bG6H=pass
[[18:41:15]] [SUCCESS] Screenshot refreshed successfully
[[18:41:15]] [SUCCESS] Screenshot refreshed successfully
[[18:41:15]] [INFO] sc2KH9bG6H=running
[[18:41:15]] [INFO] Executing action 7/50: iOS Function: text - Text: "Uno card"
[[18:41:14]] [SUCCESS] Screenshot refreshed
[[18:41:14]] [INFO] Refreshing screenshot...
[[18:41:14]] [INFO] rqLJpAP0mA=pass
[[18:41:10]] [SUCCESS] Screenshot refreshed successfully
[[18:41:10]] [SUCCESS] Screenshot refreshed successfully
[[18:41:09]] [INFO] rqLJpAP0mA=running
[[18:41:09]] [INFO] Executing action 6/50: Tap on Text: "Find"
[[18:41:09]] [SUCCESS] Screenshot refreshed
[[18:41:09]] [INFO] Refreshing screenshot...
[[18:41:08]] [SUCCESS] Screenshot refreshed
[[18:41:08]] [INFO] Refreshing screenshot...
[[18:41:04]] [SUCCESS] Screenshot refreshed successfully
[[18:41:04]] [SUCCESS] Screenshot refreshed successfully
[[18:41:04]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[18:41:03]] [SUCCESS] Screenshot refreshed
[[18:41:03]] [INFO] Refreshing screenshot...
[[18:40:59]] [SUCCESS] Screenshot refreshed successfully
[[18:40:59]] [SUCCESS] Screenshot refreshed successfully
[[18:40:59]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:40:58]] [SUCCESS] Screenshot refreshed
[[18:40:58]] [INFO] Refreshing screenshot...
[[18:40:54]] [SUCCESS] Screenshot refreshed successfully
[[18:40:54]] [SUCCESS] Screenshot refreshed successfully
[[18:40:54]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[18:40:53]] [SUCCESS] Screenshot refreshed
[[18:40:53]] [INFO] Refreshing screenshot...
[[18:40:48]] [SUCCESS] Screenshot refreshed successfully
[[18:40:48]] [SUCCESS] Screenshot refreshed successfully
[[18:40:47]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:40:47]] [SUCCESS] Screenshot refreshed
[[18:40:47]] [INFO] Refreshing screenshot...
[[18:40:43]] [SUCCESS] Screenshot refreshed successfully
[[18:40:43]] [SUCCESS] Screenshot refreshed successfully
[[18:40:43]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:40:43]] [INFO] Loaded 5 steps from test case: Kmart-NZ-Signin
[[18:40:43]] [INFO] Loading steps for multiStep action: Kmart-NZ-Signin
[[18:40:43]] [INFO] w8dueydByT=running
[[18:40:43]] [INFO] Executing action 5/50: Execute Test Case: Kmart-NZ-Signin (6 steps)
[[18:40:43]] [SUCCESS] Screenshot refreshed
[[18:40:43]] [INFO] Refreshing screenshot...
[[18:40:43]] [INFO] 3caMBvQX7k=pass
[[18:40:39]] [SUCCESS] Screenshot refreshed successfully
[[18:40:39]] [SUCCESS] Screenshot refreshed successfully
[[18:40:39]] [INFO] 3caMBvQX7k=running
[[18:40:39]] [INFO] Executing action 4/50: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:40:39]] [SUCCESS] Screenshot refreshed
[[18:40:39]] [INFO] Refreshing screenshot...
[[18:40:39]] [INFO] yUJyVO5Wev=pass
[[18:40:36]] [SUCCESS] Screenshot refreshed successfully
[[18:40:36]] [SUCCESS] Screenshot refreshed successfully
[[18:40:36]] [INFO] yUJyVO5Wev=running
[[18:40:36]] [INFO] Executing action 3/50: iOS Function: alert_accept
[[18:40:35]] [SUCCESS] Screenshot refreshed
[[18:40:35]] [INFO] Refreshing screenshot...
[[18:40:35]] [INFO] rkL0oz4kiL=pass
[[18:40:29]] [SUCCESS] Screenshot refreshed successfully
[[18:40:29]] [SUCCESS] Screenshot refreshed successfully
[[18:40:28]] [INFO] rkL0oz4kiL=running
[[18:40:28]] [INFO] Executing action 2/50: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:40:28]] [SUCCESS] Screenshot refreshed
[[18:40:28]] [INFO] Refreshing screenshot...
[[18:40:28]] [INFO] HotUJOd6oB=pass
[[18:40:22]] [INFO] HotUJOd6oB=running
[[18:40:22]] [INFO] Executing action 1/50: Restart app: env[appid]
[[18:40:22]] [INFO] ExecutionManager: Starting execution of 50 actions...
[[18:40:22]] [SUCCESS] Cleared 1 screenshots from database
[[18:40:22]] [INFO] Clearing screenshots from database before execution...
[[18:40:22]] [SUCCESS] All screenshots deleted successfully
[[18:40:22]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[18:40:22]] [INFO] Skipping report initialization - single test case execution
[[18:40:20]] [SUCCESS] All screenshots deleted successfully
[[18:40:20]] [SUCCESS] Loaded test case "WishList NZ" with 50 actions
[[18:40:20]] [SUCCESS] Added action: cleanupSteps
[[18:40:20]] [SUCCESS] Added action: swipe
[[18:40:20]] [SUCCESS] Added action: tap
[[18:40:20]] [SUCCESS] Added action: swipe
[[18:40:20]] [SUCCESS] Added action: tap
[[18:40:20]] [SUCCESS] Added action: tapOnText
[[18:40:20]] [SUCCESS] Added action: ifElseSteps
[[18:40:20]] [SUCCESS] Added action: tapOnText
[[18:40:20]] [SUCCESS] Added action: ifElseSteps
[[18:40:20]] [SUCCESS] Added action: tap
[[18:40:20]] [SUCCESS] Added action: tap
[[18:40:20]] [SUCCESS] Added action: tap
[[18:40:20]] [SUCCESS] Added action: waitTill
[[18:40:20]] [SUCCESS] Added action: swipeTillVisible
[[18:40:20]] [SUCCESS] Added action: tapIfLocatorExists
[[18:40:20]] [SUCCESS] Added action: tap
[[18:40:20]] [SUCCESS] Added action: waitTill
[[18:40:20]] [SUCCESS] Added action: tap
[[18:40:20]] [SUCCESS] Added action: tapOnText
[[18:40:20]] [SUCCESS] Added action: wait
[[18:40:20]] [SUCCESS] Added action: tap
[[18:40:20]] [SUCCESS] Added action: tapOnText
[[18:40:20]] [SUCCESS] Added action: wait
[[18:40:20]] [SUCCESS] Added action: tap
[[18:40:20]] [SUCCESS] Added action: waitTill
[[18:40:20]] [SUCCESS] Added action: tap
[[18:40:20]] [SUCCESS] Added action: tap
[[18:40:20]] [SUCCESS] Added action: swipeTillVisible
[[18:40:20]] [SUCCESS] Added action: waitTill
[[18:40:20]] [SUCCESS] Added action: tap
[[18:40:20]] [SUCCESS] Added action: waitTill
[[18:40:20]] [SUCCESS] Added action: tap
[[18:40:20]] [SUCCESS] Added action: tap
[[18:40:20]] [SUCCESS] Added action: swipeTillVisible
[[18:40:20]] [SUCCESS] Added action: waitTill
[[18:40:20]] [SUCCESS] Added action: tap
[[18:40:20]] [SUCCESS] Added action: tap
[[18:40:20]] [SUCCESS] Added action: tap
[[18:40:20]] [SUCCESS] Added action: swipeTillVisible
[[18:40:20]] [SUCCESS] Added action: waitTill
[[18:40:20]] [SUCCESS] Added action: tap
[[18:40:20]] [SUCCESS] Added action: waitTill
[[18:40:20]] [SUCCESS] Added action: waitTill
[[18:40:20]] [SUCCESS] Added action: iosFunctions
[[18:40:20]] [SUCCESS] Added action: tapOnText
[[18:40:20]] [SUCCESS] Added action: multiStep
[[18:40:20]] [SUCCESS] Added action: waitTill
[[18:40:20]] [SUCCESS] Added action: iosFunctions
[[18:40:20]] [SUCCESS] Added action: tap
[[18:40:20]] [SUCCESS] Added action: restartApp
[[18:40:20]] [INFO] All actions cleared
[[18:40:20]] [INFO] Cleaning up screenshots...
[[18:40:19]] [SUCCESS] Screenshot refreshed successfully
[[18:40:18]] [SUCCESS] Screenshot refreshed
[[18:40:18]] [INFO] Refreshing screenshot...
[[18:40:17]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[18:40:17]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[18:40:15]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[18:40:13]] [SUCCESS] Found 1 device(s)
[[18:40:12]] [INFO] Refreshing device list...
