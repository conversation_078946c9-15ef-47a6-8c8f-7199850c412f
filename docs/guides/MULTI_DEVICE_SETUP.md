# Multi-Device <PERSON>l Testing Setup

This document explains how to run multiple instances of the Mobile App Automation Tool for parallel device testing.

> **📖 For detailed multi-session setup with different devices, see [MULTI_SESSION_SETUP.md](MULTI_SESSION_SETUP.md)**

## Overview

The application now supports configurable ports for:
- **Flask Server Port** (default: 8080) - Web interface
- **Appium Server Port** (default: 4723) - Appium automation server
- **WebDriverAgent Port** (default: 8100) - iOS device communication

## Quick Start

### Method 1: Using the Helper Script

```bash
# Start 2 instances with default ports
./start_multi_device.sh

# Start 3 instances
./start_multi_device.sh --instances 3

# Start with custom base ports
./start_multi_device.sh --base-port 9000 --base-appium 5000 --base-wda 9100
```

### Method 2: Manual Start

Open multiple terminals and run:

```bash
# Terminal 1 - Instance 1
source venv/bin/activate
python run.py --port 8081 --appium-port 4724 --wda-port 8101

# Terminal 2 - Instance 2  
source venv/bin/activate
python run.py --port 8082 --appium-port 4725 --wda-port 8102

# Terminal 3 - Instance 3
source venv/bin/activate
python run.py --port 8083 --appium-port 4726 --wda-port 8103
```

## Command Line Arguments

| Argument | Description | Default |
|----------|-------------|---------|
| `--port` | Flask web server port | 8080 |
| `--appium-port` | Appium server port | 4723 |
| `--wda-port` | WebDriverAgent port | 8100 |

## Port Configuration

### Default Port Ranges
- **Flask**: 8080, 8081, 8082, 8083...
- **Appium**: 4723, 4724, 4725, 4726...
- **WebDriverAgent**: 8100, 8101, 8102, 8103...

### Environment Variables
You can also set default ports using environment variables:
```bash
export FLASK_PORT=9000
export APPIUM_PORT=5000
export WDA_PORT=9100
python run.py
```

## Device Connection

1. **Start multiple app instances** using different ports
2. **Connect different devices** to different instances
3. **Run tests in parallel** across multiple devices

### Example Workflow

1. Start 3 instances:
   ```bash
   ./start_multi_device.sh --instances 3
   ```

2. Access the web interfaces:
   - Instance 1: http://localhost:8080
   - Instance 2: http://localhost:8081  
   - Instance 3: http://localhost:8082

3. Connect devices:
   - Connect iPhone to Instance 1
   - Connect Android Phone to Instance 2
   - Connect iPad to Instance 3

4. Run tests simultaneously on all devices

## WebDriverAgent Setup for iOS

For iOS devices, you may need to start WebDriverAgent on different ports:

```bash
# Set WDA port and restart
export WDA_PORT=8101
./restart_wda.sh

# Or use the WDA runner directly
python WDA_agent_runner.py <UDID> <TEAM_ID> --wda-port 8101
```

## Troubleshooting

### Port Conflicts
If you get port conflicts, check what's using the ports:
```bash
lsof -i :8080  # Check Flask port
lsof -i :4723  # Check Appium port
lsof -i :8100  # Check WDA port
```

### Kill Existing Processes
```bash
# Kill processes on specific ports
kill -9 $(lsof -ti:8080)
kill -9 $(lsof -ti:4723)
kill -9 $(lsof -ti:8100)
```

### Virtual Environment
Make sure you're using the virtual environment:
```bash
source venv/bin/activate
pip install -r requirements.txt
```

## Configuration Files

The app automatically updates these configuration files with the specified ports:
- `config.py` - Global configuration
- `app/utils/appium_device_controller.py` - Appium connection settings

## Benefits of Multi-Device Testing

1. **Parallel Execution** - Run tests on multiple devices simultaneously
2. **Cross-Platform Testing** - Test iOS and Android devices in parallel
3. **Faster Feedback** - Reduce overall test execution time
4. **Device Isolation** - Each instance manages its own device connections
5. **Scalability** - Easy to add more devices and instances

## Best Practices

1. **Use consistent port ranges** to avoid conflicts
2. **Document your port assignments** for team collaboration
3. **Monitor resource usage** when running multiple instances
4. **Use different test suites** for different device types
5. **Implement proper cleanup** when stopping instances

## Example Multi-Device Test Scenarios

### Scenario 1: Cross-Platform Testing
- Instance 1 (8080): iOS iPhone - Login flow tests
- Instance 2 (8081): Android Phone - Payment flow tests
- Instance 3 (8082): iPad - UI responsiveness tests

### Scenario 2: Load Testing
- Multiple instances running the same test suite
- Different user scenarios on each device
- Concurrent API testing

### Scenario 3: Regression Testing
- Parallel execution of regression test suites
- Different app versions on different devices
- Automated comparison of results
