{"name": "WishList", "created": "2025-06-27 08:11:28", "device_id": "********-00186C801E13C01E", "actions": [{"action_id": "HotUJOd6oB", "executionTime": "3501ms", "package_id": "env[appid]", "timestamp": *************, "type": "restartApp"}, {"action_id": "rkL0oz4kiL", "executionTime": "9914ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "yUJyVO5Wev", "executionTime": "1244ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "3caMBvQX7k", "executionTime": "3309ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 30, "timestamp": *************, "type": "waitTill"}, {"action_id": "LDkFLWks00", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "3FBGGKUMbh", "enter": true, "function_name": "text", "text": "env[uname-op]", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "T3MmUw30SF", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "timeout": 10, "timestamp": 1749434481083, "type": "tap"}, {"action_id": "YqMEb5Jr6o", "enter": true, "function_name": "text", "text": "Wonderbaby@6", "timestamp": 1749435014969, "type": "iosFunctions"}, {"action_id": "yiKyF5FJwN", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "timeout": 20, "timestamp": 1749434558505, "type": "exists"}, {"action_id": "rqLJpAP0mA", "double_tap": false, "executionTime": "2302ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1745980305734, "type": "tapOnText"}, {"action_id": "sc2KH9bG6H", "executionTime": "3697ms", "function_name": "text", "text": "Uno card", "timestamp": 1745484826180, "type": "iosFunctions"}, {"action_id": "nAB6Q8LAdv", "executionTime": "5476ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 30, "timestamp": 1745485041367, "type": "waitTill"}, {"action_id": "eLxHVWKeDQ", "executionTime": "4632ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "method": "locator", "timeout": 10, "timestamp": 1746836741255, "type": "tap"}, {"action_id": "ITHvSyXXmu", "executionTime": "6970ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"SKU :\"]", "timeout": 30, "timestamp": 1746837237441, "type": "waitTill"}, {"action_id": "H3IAmq3r3i", "count": 3, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "14234ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "start_x": 50, "start_y": 70, "timestamp": 1746837343917, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "WbxRVpWtjw", "executionTime": "5432ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "method": "locator", "timeout": 10, "timestamp": 1746837373321, "type": "tap"}, {"action_id": "eLxHVWKeDQ", "executionTime": "18720ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"You may also like\"]/following-sibling::*[1]//XCUIElementTypeLink)[1]", "method": "locator", "timeout": 10, "timestamp": 1748258328372, "type": "tap"}, {"action_id": "ITHvSyXXmu", "executionTime": "6835ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"SKU :\"]", "timeout": 30, "timestamp": 1748259904903, "type": "waitTill"}, {"action_id": "H3IAmq3r3i", "count": 3, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "11455ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "start_x": 50, "start_y": 70, "timestamp": 1748259858069, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "WbxRVpWtjw", "executionTime": "4572ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "method": "locator", "timeout": 10, "timestamp": 1748259866784, "type": "tap"}, {"action_id": "j1JjmfPRaE", "package_id": "env[appid]", "timestamp": 1748314437745, "type": "restartApp"}, {"action_id": "fMzoZJg9I7", "double_tap": false, "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1748314463271, "type": "tapOnText"}, {"action_id": "ghzdMuwrHj", "enter": true, "function_name": "text", "text": "P_43386093", "timestamp": 1748314515058, "type": "iosFunctions"}, {"action_id": "eLxHVWKeDQ", "executionTime": "4632ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "method": "locator", "timeout": 10, "timestamp": 1748314619679, "type": "tap"}, {"action_id": "uOt2cFGhGr", "executionTime": "11808ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"SKU :\"]", "timeout": 30, "timestamp": 1748314525969, "type": "waitTill"}, {"action_id": "H3IAmq3r3i", "count": 3, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "11455ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "start_x": 50, "start_y": 70, "timestamp": 1748314546104, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "WbxRVpWtjw", "executionTime": "4572ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "method": "locator", "timeout": 10, "timestamp": 1748315417635, "type": "tap"}, {"action_id": "F1olhgKhUt", "executionTime": "6620ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1748258009231, "type": "tap"}, {"action_id": "Q0fomJIDoQ", "executionTime": "6270ms", "image_filename": "banner-close-updated.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "method": "locator", "threshold": 0.7, "timeout": 20, "timestamp": 1746100313636, "type": "waitTill"}, {"action_id": "Q0fomJIDoQ", "executionTime": "9330ms", "image_filename": "banner-close-updated.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746842287810, "type": "tap"}, {"action_id": "y4i304JeJj", "executionTime": "3801ms", "text_to_find": "Move", "timeout": 30, "timestamp": 1746838675751, "type": "tapOnText"}, {"action_id": "Q0fomJIDoQ", "executionTime": "11084ms", "image_filename": "banner-close-updated.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[2]/following-sibling::XCUIElementTypeImage[2]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746838700277, "type": "tap"}, {"action_id": "<PERSON><PERSON><PERSON>ynQyu", "double_tap": false, "executionTime": "3221ms", "text_to_find": "Remove", "timeout": 30, "timestamp": 1746838738528, "type": "tapOnText"}, {"action_id": "Q0fomJIDoQ", "executionTime": "4184ms", "image_filename": "banner-close-updated.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[1]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746838766517, "type": "tap"}, {"action_id": "uOt2cFGhGr", "executionTime": "11808ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"SKU :\"]", "timeout": 30, "timestamp": 1746838817503, "type": "waitTill"}, {"action_id": "lWIRxRm6HE", "executionTime": "5127ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746840700159, "type": "tap"}, {"action_id": "Ob26qqcA0p", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "20455ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1748260434324, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "Qbg9bipTGs", "count": 1, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "9519ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Move to wishlist\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 30, "timestamp": 1746102698568, "type": "waitTill", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "pr9o8Zsm5p", "executionTime": "5932ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Move to wishlist\"]", "method": "locator", "timeout": 10, "timestamp": 1746102873316, "type": "tap"}, {"action_id": "8umPSX0vrr", "executionTime": "3335ms", "image_filename": "banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1746704751098, "type": "tap"}, {"action_id": "F1olhgKhUt", "executionTime": "8774ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746842232923, "type": "tap"}, {"action_id": "Q0fomJIDoQ", "condition": {"locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "timeout": 10}, "condition_type": "exists", "executionTime": "9120ms", "image_filename": "banner-close-updated.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "method": "locator", "then_action": {"locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "method": "locator", "timeout": 10, "type": "tap"}, "threshold": 0.7, "timeout": 10, "timestamp": 1746842287165, "type": "ifElseSteps"}, {"action_id": "<PERSON><PERSON><PERSON>ynQyu", "double_tap": false, "executionTime": "3121ms", "text_to_find": "Remove", "timeout": 30, "timestamp": 1746842300771, "type": "tapOnText"}, {"action_id": "Q0fomJIDoQ", "condition": {"locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "timeout": 10}, "condition_type": "exists", "executionTime": "6759ms", "image_filename": "banner-close-updated.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "method": "locator", "then_action": {"locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "method": "locator", "timeout": 10, "type": "tap"}, "threshold": 0.7, "timeout": 10, "timestamp": 1746842396053, "type": "ifElseSteps"}, {"action_id": "<PERSON><PERSON><PERSON>ynQyu", "double_tap": false, "executionTime": "3053ms", "text_to_find": "Remove", "timeout": 30, "timestamp": 1746843339902, "type": "tapOnText"}, {"action_id": "k3mu9Mt7Ec", "executionTime": "3977ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746100354856, "type": "tap"}, {"action_id": "Ob26qqcA0p", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "8112ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746100402404, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "OyUowAaBzD", "executionTime": "3974ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746100379226, "type": "tap"}, {"type": "cleanupSteps", "timestamp": 1750975885493, "test_case_id": "Kmart_AU_Cleanup_20250626204013.json", "test_case_name": "Kmart_AU_Cleanup", "test_case_steps_count": 0, "action_id": "DYWpUY7xB6"}], "labels": [], "updated": "2025-06-27 08:11:28", "test_case_id": "tc_c0acdf3379cb"}