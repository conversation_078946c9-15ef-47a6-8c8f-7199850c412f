<!-- Tap Fallback Modal -->
<div class="modal fade" id="tapFallbackModal" tabindex="-1" aria-labelledby="tapFallbackModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="tapFallbackModalLabel">Add Fallback Action</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="text-muted">
                    Add a fallback action that will be used if the primary tap action fails.
                    The fallback will be attempted after the primary action times out.
                </p>

                <div class="form-group mb-3">
                    <label for="tapFallbackType">Fallback Type</label>
                    <select class="form-select" id="tapFallbackType">
                        <option value="">Select a fallback type</option>
                        <option value="coordinates">Tap by Coordinates</option>
                        <option value="image">Tap on Image</option>
                        <option value="text">Tap on Text</option>
                        <option value="locator">Tap by Locator</option>
                    </select>
                </div>

                <!-- Coordinates Fallback -->
                <div id="fallback-coordinates-container" class="fallback-option" style="display: none;">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="fallbackTapX">X Coordinate</label>
                                <input type="number" class="form-control" id="fallbackTapX" placeholder="X coordinate">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="fallbackTapY">Y Coordinate</label>
                                <input type="number" class="form-control" id="fallbackTapY" placeholder="Y coordinate">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Image Fallback -->
                <div id="fallback-image-container" class="fallback-option" style="display: none;">
                    <div class="form-group mb-3">
                        <label for="fallbackTapImageFilename">Reference Image</label>
                        <div class="input-group mb-2">
                            <select class="form-select" id="fallbackTapImageFilename">
                                <option value="" selected disabled>Select an image...</option>
                                <!-- Options will be populated by JS -->
                            </select>
                            <button class="btn btn-outline-secondary" type="button" id="refreshFallbackTapImages">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="fallbackTapThreshold">Threshold (0.1-1.0)</label>
                                <input type="number" class="form-control" id="fallbackTapThreshold" placeholder="Threshold" value="0.7" min="0.1" max="1.0" step="0.1">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Text Fallback -->
                <div id="fallback-text-container" class="fallback-option" style="display: none;">
                    <div class="form-group mb-3">
                        <label for="fallbackTapText">Text to Find</label>
                        <input type="text" class="form-control" id="fallbackTapText" placeholder="Enter text to find on screen">
                    </div>
                </div>

                <!-- Locator Fallback -->
                <div id="fallback-locator-container" class="fallback-option" style="display: none;">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="fallbackTapLocatorType">Locator Type</label>
                                <select class="form-select" id="fallbackTapLocatorType">
                                    <option value="id">ID</option>
                                    <option value="xpath">XPath</option>
                                    <option value="uiselector">UI Selector</option>
                                    <option value="name">Name</option>
                                    <option value="class">Class</option>
                                    <option value="css">CSS Selector</option>
                                    <option value="accessibility_id">Accessibility ID</option>
                                    <option value="android_uiautomator">Android UIAutomator</option>
                                    <option value="ios_predicate">iOS Predicate</option>
                                    <option value="ios_class_chain">iOS Class Chain</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="fallbackTapLocatorValue">Locator Value</label>
                                <input type="text" class="form-control" id="fallbackTapLocatorValue" placeholder="Enter locator value">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveTapFallback">Save Fallback</button>
            </div>
        </div>
    </div>
</div>
