#!/usr/bin/env python3
"""
Comprehensive test script for Android automation framework improvements
Tests all three improvements: UI Selector support, ID locator fixes, and AirTest label removal
"""

import sys
import os
import re
import json
from pathlib import Path

def test_ui_selector_dropdown_options():
    """Test that UI Selector options are present in all required forms"""
    print("🔍 Testing UI Selector Dropdown Options...")
    
    # Test actionFormManager.js for Wait Till Element
    js_file = Path("app_android/static/js/modules/actionFormManager.js")
    if js_file.exists():
        content = js_file.read_text()
        if 'value="uiselector">UI Selector</option>' in content:
            print("  ✅ Wait Till Element form has UI Selector option")
        else:
            print("  ❌ Wait Till Element form missing UI Selector option")
            return False
    
    # Test index.html for all other forms
    html_file = Path("app_android/templates/index.html")
    if html_file.exists():
        content = html_file.read_text()
        
        # Count UI Selector occurrences
        ui_selector_count = len(re.findall(r'value="uiselector">UI Selector</option>', content))
        print(f"  ✅ Found {ui_selector_count} UI Selector options in HTML template")
        
        # Check specific required forms
        required_forms = [
            "getValueLocatorType",
            "compareValueLocatorType", 
            "ifExistsLocatorType",
            "ifVisibleLocatorType",
            "ifContainsTextLocatorType",
            "ifValueEqualsLocatorType",
            "ifValueContainsLocatorType",
            "ifHasAttributeLocatorType"
        ]
        
        missing_forms = []
        for form in required_forms:
            # Check if the form exists and has uiselector option nearby
            form_pattern = f'{form}.*?uiselector'
            if not re.search(form_pattern, content, re.DOTALL):
                missing_forms.append(form)
        
        if missing_forms:
            print(f"  ❌ Missing UI Selector in forms: {missing_forms}")
            return False
        else:
            print(f"  ✅ All {len(required_forms)} required forms have UI Selector support")
    
    return True

def test_javascript_placeholder_handling():
    """Test that JavaScript properly handles UI Selector placeholder text"""
    print("\n🔍 Testing JavaScript Placeholder Handling...")
    
    js_file = Path("app_android/static/js/modules/actionFormManager.js")
    if js_file.exists():
        content = js_file.read_text()
        
        # Check for UI Selector placeholder text
        placeholder_pattern = r'new UiSelector\(\)\.text\("Button"\)\.className\("android\.widget\.Button"\)'
        placeholder_count = len(re.findall(placeholder_pattern, content))
        
        if placeholder_count >= 6:  # Should be in multiple handler methods
            print(f"  ✅ Found {placeholder_count} UI Selector placeholder implementations")
        else:
            print(f"  ❌ Insufficient UI Selector placeholder implementations: {placeholder_count}")
            return False
        
        # Check for specific handler methods
        required_handlers = [
            "handleGetValueLocatorTypeChange",
            "handleCompareValueLocatorTypeChange",
            "handleIfVisibleLocatorTypeChange",
            "handleIfContainsTextLocatorTypeChange",
            "handleIfValueEqualsLocatorTypeChange",
            "handleIfValueContainsLocatorTypeChange",
            "handleIfHasAttributeLocatorTypeChange"
        ]
        
        missing_handlers = []
        for handler in required_handlers:
            if handler not in content:
                missing_handlers.append(handler)
        
        if missing_handlers:
            print(f"  ❌ Missing handler methods: {missing_handlers}")
            return False
        else:
            print(f"  ✅ All {len(required_handlers)} required handler methods found")
    
    # Check main.js for event listeners
    main_js_file = Path("app_android/static/js/main.js")
    if main_js_file.exists():
        content = main_js_file.read_text()
        
        # Check for event listener registrations
        required_listeners = [
            "getValueLocatorType",
            "compareValueLocatorType",
            "ifVisibleLocatorType",
            "ifContainsTextLocatorType",
            "ifValueEqualsLocatorType",
            "ifValueContainsLocatorType",
            "ifHasAttributeLocatorType"
        ]
        
        missing_listeners = []
        for listener in required_listeners:
            listener_pattern = f'{listener}.*addEventListener'
            if not re.search(listener_pattern, content, re.DOTALL):
                missing_listeners.append(listener)
        
        if missing_listeners:
            print(f"  ❌ Missing event listeners: {missing_listeners}")
            return False
        else:
            print(f"  ✅ All {len(required_listeners)} required event listeners found")
    
    return True

def test_android_id_locator_fixes():
    """Test Android ID locator improvements"""
    print("\n🔍 Testing Android ID Locator Fixes...")
    
    base_action_file = Path("app_android/actions/base_action.py")
    if base_action_file.exists():
        content = base_action_file.read_text()
        
        # Test AppiumBy.ID usage
        if "AppiumBy.ID" in content:
            print("  ✅ Uses AppiumBy.ID for proper Android resource-id handling")
        else:
            print("  ❌ Missing AppiumBy.ID usage")
            return False
        
        # Test retry mechanism
        if "max_retries = 3" in content and "retry_delay = 2" in content:
            print("  ✅ Retry mechanism implemented (3 attempts, 2-second delays)")
        else:
            print("  ❌ Missing or incorrect retry mechanism")
            return False
        
        # Test minimum timeout
        if "timeout < 10" in content and "timeout = 10" in content:
            print("  ✅ Minimum 10-second timeout implemented")
        else:
            print("  ❌ Missing minimum timeout handling")
            return False
        
        # Test enhanced error messages
        if "resource-id" in content and "UI Selector" in content:
            print("  ✅ Enhanced error messages with Android-specific guidance")
        else:
            print("  ❌ Missing enhanced error messages")
            return False
        
        # Test MobileBy fallback
        if "MobileBy.ID" in content and "ImportError" in content:
            print("  ✅ MobileBy fallback for older Appium versions")
        else:
            print("  ❌ Missing MobileBy fallback")
            return False
    
    return True

def test_airtest_label_removal():
    """Test AirTest label removal"""
    print("\n🔍 Testing AirTest Label Removal...")
    
    main_js_file = Path("app_android/static/js/main.js")
    if main_js_file.exists():
        content = main_js_file.read_text()
        
        # Check that badge creation code is removed
        badge_patterns = [
            "airTestBadge.innerHTML",
            "airTestBadge.className",
            "AirTest.*badge",
            "badge.*AirTest"
        ]
        
        found_badge_code = []
        for pattern in badge_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                found_badge_code.append(pattern)
        
        if found_badge_code:
            print(f"  ❌ Found AirTest badge code: {found_badge_code}")
            return False
        else:
            print("  ✅ AirTest badge creation/removal code completely removed")
        
        # Check that connection logging is clean
        if "Connected to device:" in content:
            # Find the connection logging line
            connection_lines = [line.strip() for line in content.split('\n') if 'Connected to device:' in line]
            if connection_lines:
                # Should be simple without AirTest references
                clean_logging = all('AirTest' not in line for line in connection_lines)
                if clean_logging:
                    print("  ✅ Clean device connection logging without AirTest references")
                else:
                    print("  ❌ Device connection logging still contains AirTest references")
                    return False
    
    return True

def test_import_compatibility():
    """Test that critical imports work correctly"""
    print("\n🔍 Testing Import Compatibility...")
    
    try:
        # Test that base_action can be imported
        sys.path.insert(0, 'app_android')
        from actions.base_action import BaseAction
        print("  ✅ BaseAction imports successfully")
        
        # Test AppiumBy import
        from appium.webdriver.common.appiumby import AppiumBy
        print("  ✅ AppiumBy imports successfully")
        
        # Test that the locator mapping includes AppiumBy.ID
        if hasattr(AppiumBy, 'ID'):
            print("  ✅ AppiumBy.ID is available")
        else:
            print("  ❌ AppiumBy.ID not available")
            return False
        
        return True
        
    except ImportError as e:
        print(f"  ❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"  ❌ Unexpected error: {e}")
        return False

def main():
    """Run comprehensive testing protocol"""
    print("🚀 Android Automation Framework Improvements - Comprehensive Testing")
    print("=" * 80)
    
    # Run all tests
    tests = [
        ("UI Selector Dropdown Options", test_ui_selector_dropdown_options),
        ("JavaScript Placeholder Handling", test_javascript_placeholder_handling),
        ("Android ID Locator Fixes", test_android_id_locator_fixes),
        ("AirTest Label Removal", test_airtest_label_removal),
        ("Import Compatibility", test_import_compatibility)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 50)
        result = test_func()
        results.append((test_name, result))
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 COMPREHENSIVE TEST RESULTS")
    print("=" * 80)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n📋 MANUAL TESTING CHECKLIST:")
        print("1. Start Android app: python3 run_android.py (port 8081)")
        print("2. Test UI Selector dropdowns in all action forms")
        print("3. Test ID locators with Android resource-id format")
        print("4. Verify no AirTest badge appears on device connection")
        print("5. Test placeholder text changes when selecting UI Selector")
        return 0
    else:
        print(f"\n❌ {total - passed} tests failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
