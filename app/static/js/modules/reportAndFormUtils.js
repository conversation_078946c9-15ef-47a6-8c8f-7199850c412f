// reportAndFormUtils.js

class ReportAndFormUtils {
    constructor(app) {
        this.app = app; // Reference to the main AppiumAutomationApp instance
    }

    showReportButton(reportUrl) {
        // Remove any existing report button
        const existingButton = document.getElementById('viewReportButton');
        if (existingButton) {
            existingButton.remove();
        }

        // Create a floating action button
        const button = document.createElement('div');
        button.id = 'viewReportButton';
        button.className = 'position-fixed bottom-0 end-0 m-4';
        button.innerHTML = `
            <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header bg-success text-white">
                    <strong class="me-auto"><i class="bi bi-file-earmark-check"></i> Test Report Ready</strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    <p>Your test execution report is ready to view.</p>
                    <div class="mt-2 pt-2 border-top d-flex justify-content-between">
                        <a href="${reportUrl}" target="_blank" class="btn btn-primary btn-sm">
                            <i class="bi bi-eye"></i> View Report
                        </a>
                        <button type="button" class="btn btn-outline-secondary btn-sm" id="reportTabBtn">
                            <i class="bi bi-bar-chart"></i> Reports Tab
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Add to document
        document.body.appendChild(button);

        // Add event listeners
        const closeBtn = button.querySelector('.btn-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                button.remove();
            });
        }

        const reportsTabBtn = button.querySelector('#reportTabBtn');
        if (reportsTabBtn) {
            reportsTabBtn.addEventListener('click', () => {
                // Show reports tab
                const reportsTab = document.getElementById('reports-tab-btn');
                if (reportsTab) {
                    reportsTab.click();
                    button.remove();
                }
            });
        }
    }

    createTapActionForm(container, prefix = 'then') {
        container.innerHTML = `
            <div class="mb-3">
                <ul class="nav nav-tabs" id="${prefix}TapTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="${prefix}-tap-coordinates-tab" data-bs-toggle="tab" data-bs-target="#${prefix}-tap-coordinates" type="button" role="tab" aria-controls="${prefix}-tap-coordinates" aria-selected="true">Coordinates</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="${prefix}-tap-image-tab" data-bs-toggle="tab" data-bs-target="#${prefix}-tap-image" type="button" role="tab" aria-controls="${prefix}-tap-image" aria-selected="false">Image</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="${prefix}-tap-locator-tab" data-bs-toggle="tab" data-bs-target="#${prefix}-tap-locator" type="button" role="tab" aria-controls="${prefix}-tap-locator" aria-selected="false">Locator</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="${prefix}-tap-text-tab" data-bs-toggle="tab" data-bs-target="#${prefix}-tap-text" type="button" role="tab" aria-controls="${prefix}-tap-text" aria-selected="false">Text</button>
                    </li>
                </ul>

                <div class="tab-content mt-3" id="${prefix}TapTabContent">
                    <!-- Coordinates Tab -->
                    <div class="tab-pane fade show active" id="${prefix}-tap-coordinates" role="tabpanel" aria-labelledby="${prefix}-tap-coordinates-tab">
                        <div class="form-group mb-3">
                            <label>X Coordinate</label>
                            <input type="number" id="${prefix}TapX" class="form-control" value="0" min="0">
                        </div>
                        <div class="form-group mb-3">
                            <label>Y Coordinate</label>
                            <input type="number" id="${prefix}TapY" class="form-control" value="0" min="0">
                        </div>
                    </div>

                    <!-- Image Tab -->
                    <div class="tab-pane fade" id="${prefix}-tap-image" role="tabpanel" aria-labelledby="${prefix}-tap-image-tab">
                        <div class="form-group mb-3">
                            <label>Reference Image</label>
                            <div class="input-group mb-2">
                                <select class="form-select" id="${prefix}TapImageFilename">
                                    <option value="" selected disabled>Select an image...</option>
                                    <!-- Options populated by JS -->
                                </select>
                                <button class="btn btn-outline-secondary" type="button" id="${prefix}RefreshTapImages">
                                    <i class="bi bi-arrow-clockwise"></i>
                                </button>
                            </div>
                        </div>
                        <div class="form-group mb-3">
                            <label>Similarity Threshold</label>
                            <input type="number" id="${prefix}TapImageThreshold" class="form-control" value="0.7" min="0" max="1" step="0.05">
                            <small class="form-text text-muted">Lower values are less strict (0.1-1.0)</small>
                        </div>
                        <div class="form-group mb-3">
                            <label>Timeout (seconds)</label>
                            <input type="number" id="${prefix}TapImageTimeout" class="form-control" value="20" min="1" step="1">
                        </div>
                    </div>

                    <!-- Locator Tab -->
                    <div class="tab-pane fade" id="${prefix}-tap-locator" role="tabpanel" aria-labelledby="${prefix}-tap-locator-tab">
                        <div class="form-group mb-3">
                            <label>Locator Type</label>
                            <select id="${prefix}TapLocatorType" class="form-control">
                                <option value="id">ID</option>
                                <option value="xpath">XPath</option>
                                <option value="accessibility_id">Accessibility ID</option>
                                <option value="uiselector">UI Selector</option>
                                <option value="class">Class Name</option>
                                <option value="name">Name</option>
                            </select>
                        </div>
                        <div class="form-group mb-3">
                            <label>Locator Value</label>
                            <input type="text" id="${prefix}TapLocatorValue" class="form-control" placeholder="Enter locator value">
                        </div>
                        <div class="form-group mb-3">
                            <label>Timeout (seconds)</label>
                            <input type="number" id="${prefix}TapLocatorTimeout" class="form-control" value="10" min="1" step="1">
                        </div>
                    </div>

                    <!-- Text Tab -->
                    <div class="tab-pane fade" id="${prefix}-tap-text" role="tabpanel" aria-labelledby="${prefix}-tap-text-tab">
                        <div class="form-group mb-3">
                            <label>Text to Find & Tap</label>
                            <input type="text" id="${prefix}TapOnTextToFind" class="form-control" placeholder="Enter exact text visible on screen">
                            <small class="text-muted">Uses OCR to find this text and tap its center.</small>
                        </div>
                        <div class="form-group mb-3">
                            <label>Timeout (seconds)</label>
                            <input type="number" id="${prefix}TapOnTextTimeout" class="form-control" value="30" min="1" step="1">
                            <small class="text-muted">How long to search for the text (default: 30s).</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
        if (this.app) {
            this.app.loadReferenceImages('tap', `${prefix}TapImageFilename`);
            const refreshBtn = document.getElementById(`${prefix}RefreshTapImages`);
            if (refreshBtn) {
                refreshBtn.addEventListener('click', () => this.app.loadReferenceImages('tap', `${prefix}TapImageFilename`));
            }
        }
    }

    createDoubleTapActionForm(container, prefix = 'then') {
        container.innerHTML = `
            <div class="form-group mb-3">
                <label>X Coordinate</label>
                <input type="number" id="${prefix}DoubleTapX" class="form-control" value="0" min="0">
            </div>
            <div class="form-group mb-3">
                <label>Y Coordinate</label>
                <input type="number" id="${prefix}DoubleTapY" class="form-control" value="0" min="0">
            </div>
        `;
    }

    createSwipeActionForm(container, prefix = 'then') {
        container.innerHTML = `
            <div class="form-group mb-3">
                <label>Start X</label>
                <input type="number" id="${prefix}SwipeStartX" class="form-control" value="0" min="0">
            </div>
            <div class="form-group mb-3">
                <label>Start Y</label>
                <input type="number" id="${prefix}SwipeStartY" class="form-control" value="0" min="0">
            </div>
            <div class="form-group mb-3">
                <label>End X</label>
                <input type="number" id="${prefix}SwipeEndX" class="form-control" value="0" min="0">
            </div>
            <div class="form-group mb-3">
                <label>End Y</label>
                <input type="number" id="${prefix}SwipeEndY" class="form-control" value="0" min="0">
            </div>
            <div class="form-group mb-3">
                <label>Duration (ms)</label>
                <input type="number" id="${prefix}SwipeDuration" class="form-control" value="300" min="100">
            </div>
        `;
    }

    createTextActionForm(container, prefix = 'then') {
        container.innerHTML = `
            <div class="form-group mb-3">
                <label>Text</label>
                <input type="text" id="${prefix}InputText" class="form-control" placeholder="Enter text to input">
            </div>
        `;
    }

    createSendKeysActionForm(container, prefix = 'then') {
        container.innerHTML = `
            <div class="form-group mb-3">
                <label>Locator Type</label>
                <select id="${prefix}SendKeysLocatorType" class="form-control">
                    <option value="id">ID</option>
                    <option value="xpath">XPath</option>
                    <option value="accessibility_id">Accessibility ID</option>
                    <option value="class_name">Class Name</option>
                    <option value="name">Name</option>
                    <option value="css">CSS Selector</option>
                </select>
            </div>
            <div class="form-group mb-3">
                <label>Locator Value</label>
                <input type="text" id="${prefix}SendKeysLocatorValue" class="form-control" placeholder="Enter locator value">
            </div>
            <div class="form-group mb-3">
                <label>Text to Send</label>
                <input type="text" id="${prefix}SendKeysText" class="form-control" placeholder="Enter text to send">
            </div>
            <div class="form-group mb-3">
                <div class="form-check">
                    <input type="checkbox" id="${prefix}SendKeysClearFirst" class="form-check-input" checked>
                    <label class="form-check-label" for="${prefix}SendKeysClearFirst">Clear field first</label>
                </div>
            </div>
            <div class="form-group mb-3">
                <label>Timeout (seconds)</label>
                <input type="number" id="${prefix}SendKeysTimeout" class="form-control" value="15" min="1" step="1">
                <small class="text-muted">Maximum time to wait for element (default: 15s)</small>
            </div>
        `;
    }

    createKeyActionForm(container, prefix = 'then') {
        container.innerHTML = `
            <div class="form-group mb-3">
                <label>Key Code</label>
                <input type="text" id="${prefix}KeyCode" class="form-control" placeholder="e.g., 66 for 'B'">
            </div>
        `;
    }

    createWaitActionForm(container, prefix = 'then') {
        container.innerHTML = `
            <div class="form-group mb-3">
                <label>Wait Time (ms)</label>
                <input type="number" id="${prefix}WaitTime" class="form-control" value="1000" min="100">
            </div>
        `;
    }

    createClickElementActionForm(container, prefix = 'then') {
        container.innerHTML = `
            <div class="form-group mb-3">
                <label>Locator Type</label>
                <select id="${prefix}ClickElementLocatorType" class="form-control">
                    <option value="id">ID</option>
                    <option value="xpath">XPath</option>
                    <option value="accessibility_id">Accessibility ID</option>
                    <option value="uiselector">UI Selector</option>
                    <option value="class">Class Name</option>
                    <option value="name">Name</option>
                    <option value="text">Text</option>
                </select>
            </div>
            <div class="form-group mb-3">
                <label>Locator Value</label>
                <input type="text" id="${prefix}ClickElementLocator" class="form-control" placeholder="Enter locator value">
            </div>
            <div class="form-group mb-3">
                <label>Timeout (seconds)</label>
                <input type="number" id="${prefix}ClickElementTimeout" class="form-control" value="10" min="1" step="1">
            </div>
        `;
    }

    createWaitTillActionForm(container, prefix = 'then') {
        container.innerHTML = `
            <div class="form-group mb-3">
                <label>Locator Type</label>
                <select id="${prefix}WaitTillLocatorType" class="form-control">
                    <option value="id">ID</option>
                    <option value="xpath">XPath</option>
                    <option value="accessibility_id">Accessibility ID</option>
                    <option value="class">Class Name</option>
                    <option value="name">Name</option>
                    <option value="text">Text</option>
                </select>
            </div>
            <div class="form-group mb-3">
                <label>Locator Value</label>
                <input type="text" id="${prefix}WaitTillLocator" class="form-control" placeholder="Enter locator value">
            </div>
            <div class="form-group mb-3">
                <label>Timeout (seconds)</label>
                <input type="number" id="${prefix}WaitTillTimeout" class="form-control" value="10" min="1" step="1">
            </div>
        `;
    }

    createSwipeTillVisibleActionForm(container, prefix = 'then') {
        container.innerHTML = `
            <div class="form-group mb-3">
                <label>Direction</label>
                <select id="${prefix}SwipeTillVisibleDirection" class="form-control">
                    <option value="up">Up</option>
                    <option value="down">Down</option>
                    <option value="left">Left</option>
                    <option value="right">Right</option>
                </select>
            </div>
            <div class="form-group mb-3">
                <label>Max Swipe Count</label>
                <input type="number" id="${prefix}SwipeTillVisibleCount" class="form-control" value="5" min="1">
            </div>
            <div class="form-group mb-3">
                <label>Locator Type</label>
                <select id="${prefix}SwipeTillVisibleLocatorType" class="form-control">
                    <option value="id">ID</option>
                    <option value="xpath">XPath</option>
                    <option value="accessibility_id">Accessibility ID</option>
                    <option value="class">Class Name</option>
                    <option value="name">Name</option>
                    <option value="text">Text</option>
                </select>
            </div>
            <div class="form-group mb-3">
                <label>Locator Value (element to find)</label>
                <input type="text" id="${prefix}SwipeTillVisibleLocator" class="form-control" placeholder="Enter locator value">
            </div>
        `;
    }

    createTextClearActionForm(container, prefix = 'then') {
        container.innerHTML = `
            <div class="form-group mb-3">
                <label>Text</label>
                <input type="text" id="${prefix}TextClearInput" class="form-control" placeholder="Enter text to input after clearing">
            </div>
            <div class="form-group mb-3">
                <label>Delay (ms)</label>
                <input type="number" id="${prefix}TextClearDelay" class="form-control" value="500" min="100" step="100">
            </div>
        `;
    }

    createTapImageActionForm(container, prefix = 'then') {
        container.innerHTML = `
            <div class="form-group mb-3">
                <label>Reference Image</label>
                <div class="input-group mb-2">
                    <select class="form-select" id="${prefix}TapImageFilename">
                        <option value="" selected disabled>Select an image...</option>
                        <!-- Options populated by JS -->
                    </select>
                    <button class="btn btn-outline-secondary" type="button" id="${prefix}RefreshTapImages">
                        <i class="bi bi-arrow-clockwise"></i>
                    </button>
                </div>
            </div>
            <div class="form-group mb-3">
                <label>Similarity Threshold</label>
                <input type="number" id="${prefix}TapImageThreshold" class="form-control" value="0.7" min="0" max="1" step="0.05">
                <small class="form-text text-muted">Lower values are less strict (0.1-1.0)</small>
            </div>
            <div class="form-group mb-3">
                <label>Timeout (seconds)</label>
                <input type="number" id="${prefix}TapImageTimeout" class="form-control" value="20" min="1" step="1">
            </div>
        `;
        if (this.app) {
            this.app.loadReferenceImages('tap', `${prefix}TapImageFilename`);
            const refreshBtn = document.getElementById(`${prefix}RefreshTapImages`);
            if (refreshBtn) {
                refreshBtn.addEventListener('click', () => this.app.loadReferenceImages('tap', `${prefix}TapImageFilename`));
            }
        }
    }

    createIosFunctionsActionForm(container, prefix = 'then') {
        const html = `
            <div class="alert alert-info mb-3">
                <i class="bi bi-info-circle"></i> These functions are only available for iOS devices using Airtest.
            </div>
            <div class="form-group mb-3">
                <label>iOS Function</label>
                <select id="${prefix}IosFunction" class="form-control">
                    <option value="home">Press Home Button</option>
                    <option value="lock">Lock Device</option>
                    <option value="unlock">Unlock Device</option>
                    <option value="press">Press Key</option>
                    <option value="alert_accept">Accept Alert</option>
                    <option value="alert_dismiss">Dismiss Alert</option>
                    <option value="alert_click">Click Alert Button</option>
                    <option value="alert_wait">Wait for Alert</option>
                    <option value="alert_buttons">Get Alert Buttons</option>
                    <option value="alert_exists">Check if Alert Exists</option>
                    <option value="get_clipboard">Get Clipboard Content</option>
                    <option value="set_clipboard">Set Clipboard Content</option>
                    <option value="paste">Paste Clipboard Content</option>
                    <option value="get_ip_address">Get Device IP Address</option>
                    <option value="device_status">Get Device Status</option>
                    <option value="is_locked">Check if Device is Locked</option>
                    <option value="push">Push file to device</option>
                    <option value="clear_app">Clear app data</option>
                    <option value="list_app">List installed apps</option>
                </select>
            </div>

            <!-- Press Key Parameters -->
            <div id="${prefix}IosPressKeyParams" class="d-none">
                <div class="form-group mb-3">
                    <label>Key to Press</label>
                    <select id="${prefix}IosPressKey" class="form-control">
                        <option value="home">Home</option>
                        <option value="volumeUp">Volume Up</option>
                        <option value="volumeDown">Volume Down</option>
                    </select>
                </div>
            </div>

            <!-- Alert Click Parameters -->
            <div id="${prefix}IosAlertClickParams" class="d-none">
                <div class="form-group mb-3">
                    <label>Button Text</label>
                    <input type="text" id="${prefix}IosAlertButtonText" class="form-control" placeholder="Enter button text">
                </div>
            </div>

            <!-- Alert Wait Parameters -->
            <div id="${prefix}IosAlertWaitParams" class="d-none">
                <div class="form-group mb-3">
                    <label>Timeout (seconds)</label>
                    <input type="number" id="${prefix}IosAlertTimeout" class="form-control" min="1" value="2">
                </div>
            </div>

            <!-- Set Clipboard Parameters -->
            <div id="${prefix}IosSetClipboardParams" class="d-none">
                <div class="form-group mb-3">
                    <label>Content</label>
                    <input type="text" id="${prefix}IosClipboardContent" class="form-control" placeholder="Enter content to set">
                </div>
            </div>

            <!-- Push File Parameters -->
            <div id="${prefix}IosPushFileParams" class="d-none">
                 <div class="form-group mb-3">
                    <label for="${prefix}IosPushLocalPath">Local File Path</label>
                    <input type="text" id="${prefix}IosPushLocalPath" class="form-control" placeholder="e.g., /path/to/your/file.txt or files_to_push/your_file.txt">
                     <small class="text-muted">Path on your computer to the file to push. Can be absolute or relative to the 'files_to_push' directory.</small>
                </div>
                <div class="form-group mb-3">
                    <label for="${prefix}IosPushRemotePath">Remote Path (optional)</label>
                    <input type="text" id="${prefix}IosPushRemotePath" class="form-control" placeholder="e.g., /var/mobile/Media/DCIM/100APPLE/">
                    <small class="text-muted">Destination path on the device. If blank, uses a default temporary location.</small>
                </div>
            </div>

            <!-- Clear App Parameters -->
            <div id="${prefix}IosClearAppParams" class="d-none">
                <div class="form-group mb-3">
                    <label for="${prefix}IosClearAppBundleId">Bundle ID</label>
                    <input type="text" id="${prefix}IosClearAppBundleId" class="form-control" placeholder="e.g., com.example.myapp">
                    <small class="text-muted">The bundle identifier of the app to clear.</small>
                </div>
            </div>
        `;
        container.innerHTML = html;

        // Add event listener for function type change
        const iosFunctionSelect = document.getElementById(`${prefix}IosFunction`);
        if (iosFunctionSelect) {
            iosFunctionSelect.addEventListener('change', (event) => {
                this.app.actionFormManager.handleIosFunctionChange(event, prefix); // Assuming ActionFormManager holds this logic
            });
        }
    }
}

// If you are not using ES6 modules in your main.js and need this to be globally accessible:
// window.ReportAndFormUtils = ReportAndFormUtils;