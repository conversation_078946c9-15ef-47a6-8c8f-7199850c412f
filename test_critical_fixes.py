#!/usr/bin/env python3
"""
Test script for critical mobile automation framework fixes:
1. UI Selector support in iOS Wait Till Element action
2. Enhanced Android ID locator detection with fallback mechanisms
"""

import sys
import os
import re
import json
from pathlib import Path

def test_ios_wait_till_ui_selector_support():
    """Test that iOS Wait Till Element action includes UI Selector support"""
    print("\n🔍 Testing iOS Wait Till Element UI Selector Support...")
    
    ios_action_form_file = Path("app/static/js/modules/actionFormManager.js")
    if not ios_action_form_file.exists():
        print("  ❌ iOS actionFormManager.js not found")
        return False
    
    content = ios_action_form_file.read_text()
    
    # Check for UI Selector option in Wait Till Element form
    wait_till_form_pattern = r'createWaitTillActionForm.*?<option value="uiselector">UI Selector</option>'
    if re.search(wait_till_form_pattern, content, re.DOTALL):
        print("  ✅ UI Selector option found in iOS Wait Till Element form")
    else:
        print("  ❌ UI Selector option missing in iOS Wait Till Element form")
        return False
    
    # Check for UI Selector handling in event handler
    ui_selector_handler_pattern = r'type === \'uiselector\'.*?UI Selector.*?new UiSelector\(\)'
    if re.search(ui_selector_handler_pattern, content, re.DOTALL):
        print("  ✅ UI Selector event handling found in iOS Wait Till Element form")
    else:
        print("  ❌ UI Selector event handling missing in iOS Wait Till Element form")
        return False
    
    # Check for Wait Condition dropdown (should be present in updated form)
    wait_condition_pattern = r'waitTillCondition.*?<option value="visible">Visible</option>'
    if re.search(wait_condition_pattern, content, re.DOTALL):
        print("  ✅ Wait Condition dropdown found in iOS Wait Till Element form")
    else:
        print("  ❌ Wait Condition dropdown missing in iOS Wait Till Element form")
        return False
    
    # Check for threshold input (should be present for image locators)
    threshold_pattern = r'waitTillThreshold.*?value="0\.7"'
    if re.search(threshold_pattern, content, re.DOTALL):
        print("  ✅ Threshold input found in iOS Wait Till Element form")
    else:
        print("  ❌ Threshold input missing in iOS Wait Till Element form")
        return False
    
    return True

def test_android_id_locator_enhancements():
    """Test enhanced Android ID locator detection with fallback mechanisms"""
    print("\n🔍 Testing Android ID Locator Enhancements...")
    
    android_base_action_file = Path("app_android/actions/base_action.py")
    if not android_base_action_file.exists():
        print("  ❌ Android base_action.py not found")
        return False
    
    content = android_base_action_file.read_text()
    
    # Check for Android ID fallback method
    if "_find_element_by_android_id_with_fallback" in content:
        print("  ✅ Android ID fallback method found")
    else:
        print("  ❌ Android ID fallback method missing")
        return False
    
    # Check for multiple ID strategies
    strategy_patterns = [
        "Android ID Strategy 1: Trying original ID value",
        "Android ID Strategy 2: Trying with prefix",
        "Android ID Strategy 3: Trying short ID",
        "Android ID Strategy 4: Trying UI Selector fallback"
    ]
    
    missing_strategies = []
    for strategy in strategy_patterns:
        if strategy not in content:
            missing_strategies.append(strategy)
    
    if missing_strategies:
        print(f"  ❌ Missing ID strategies: {missing_strategies}")
        return False
    else:
        print(f"  ✅ All {len(strategy_patterns)} ID fallback strategies found")
    
    # Check for package prefix handling
    if "common_prefixes" in content and "android:id/" in content:
        print("  ✅ Package prefix handling found")
    else:
        print("  ❌ Package prefix handling missing")
        return False
    
    # Check for short ID extraction
    if "split(':id/')[-1]" in content:
        print("  ✅ Short ID extraction logic found")
    else:
        print("  ❌ Short ID extraction logic missing")
        return False
    
    # Check for UI Selector fallback integration
    if "new UiSelector().resourceId" in content:
        print("  ✅ UI Selector fallback integration found")
    else:
        print("  ❌ UI Selector fallback integration missing")
        return False
    
    # Check for enhanced error messages
    if "The framework tried multiple ID formats automatically" in content:
        print("  ✅ Enhanced error messages found")
    else:
        print("  ❌ Enhanced error messages missing")
        return False
    
    return True

def test_backward_compatibility():
    """Test that existing functionality remains intact"""
    print("\n🔍 Testing Backward Compatibility...")
    
    # Check that Android Wait Till Element still has UI Selector (should remain unchanged)
    android_action_form_file = Path("app_android/static/js/modules/actionFormManager.js")
    if android_action_form_file.exists():
        content = android_action_form_file.read_text()
        if 'value="uiselector">UI Selector</option>' in content:
            print("  ✅ Android Wait Till Element UI Selector support preserved")
        else:
            print("  ❌ Android Wait Till Element UI Selector support lost")
            return False
    
    # Check that original ID locator handling is still present
    android_base_action_file = Path("app_android/actions/base_action.py")
    if android_base_action_file.exists():
        content = android_base_action_file.read_text()
        if "AppiumBy.ID" in content:
            print("  ✅ Original AppiumBy.ID usage preserved")
        else:
            print("  ❌ Original AppiumBy.ID usage lost")
            return False
    
    # Check that retry mechanism is still present
    if "max_retries = 3" in content and "retry_delay = 2" in content:
        print("  ✅ Retry mechanism preserved")
    else:
        print("  ❌ Retry mechanism lost")
        return False
    
    return True

def test_integration_consistency():
    """Test that both platforms have consistent behavior where applicable"""
    print("\n🔍 Testing Integration Consistency...")
    
    # Both platforms should have Wait Till Element action
    ios_file = Path("app/static/js/modules/actionFormManager.js")
    android_file = Path("app_android/static/js/modules/actionFormManager.js")
    
    if not ios_file.exists() or not android_file.exists():
        print("  ❌ Missing action form files")
        return False
    
    ios_content = ios_file.read_text()
    android_content = android_file.read_text()
    
    # Both should have createWaitTillActionForm method
    if "createWaitTillActionForm" in ios_content and "createWaitTillActionForm" in android_content:
        print("  ✅ Both platforms have Wait Till Element action form")
    else:
        print("  ❌ Wait Till Element action form missing on one platform")
        return False
    
    # Both should have basic locator types (id, xpath, accessibility_id)
    basic_locators = ['value="id">ID</option>', 'value="xpath">XPath</option>', 'value="accessibility_id">Accessibility ID</option>']
    
    for locator in basic_locators:
        if locator in ios_content and locator in android_content:
            print(f"  ✅ Both platforms support {locator.split('>')[1].split('<')[0]} locator")
        else:
            print(f"  ❌ Inconsistent {locator.split('>')[1].split('<')[0]} locator support")
            return False
    
    return True

def main():
    """Run comprehensive testing for critical fixes"""
    print("🚀 Critical Mobile Automation Framework Fixes - Validation Testing")
    print("=" * 80)
    
    # Run all tests
    tests = [
        ("iOS Wait Till Element UI Selector Support", test_ios_wait_till_ui_selector_support),
        ("Android ID Locator Enhancements", test_android_id_locator_enhancements),
        ("Backward Compatibility", test_backward_compatibility),
        ("Integration Consistency", test_integration_consistency)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 50)
        result = test_func()
        results.append((test_name, result))
        
        if result:
            print(f"  ✅ {test_name}: PASSED")
        else:
            print(f"  ❌ {test_name}: FAILED")
    
    # Summary
    print(f"\n📊 Test Summary")
    print("=" * 50)
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {status}: {test_name}")
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL CRITICAL FIXES VALIDATED SUCCESSFULLY!")
        return True
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
